# 视频平台数据库部署指南

## 📋 概述

本文档包含视频平台的完整数据库结构和部署说明。

## 🗄️ 数据库信息

- **数据库名称**: `zhengshiban_dev` (开发环境) / `zhengshiban_prod` (生产环境)
- **字符集**: `utf8mb4`
- **排序规则**: `utf8mb4_unicode_ci`
- **MySQL版本要求**: 5.7+ 或 8.0+

## 📁 文件说明

### 1. `complete_database_structure.sql`
完整的数据库表结构文件，包含：
- 用户相关表 (users, admins)
- 视频相关表 (videos, video_categories, video_processing_status, video_likes)
- 采集系统表 (collect_sources, collect_tasks, collect_category_mapping, etc.)
- 系统配置表 (system_configs, system_settings)
- 外键约束

### 2. `initial_data.sql`
初始数据文件，包含：
- 默认管理员账户 (admin/password)
- 系统用户账户
- 基础视频分类
- 360资源网采集源配置
- 完整的分类映射 (51个分类)
- 系统配置和设置

## 🚀 部署步骤

### 步骤1: 创建数据库
```sql
CREATE DATABASE zhengshiban_prod CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 步骤2: 导入表结构
```bash
mysql -u root -p zhengshiban_prod < complete_database_structure.sql
```

### 步骤3: 导入初始数据
```bash
mysql -u root -p zhengshiban_prod < initial_data.sql
```

### 步骤4: 验证部署
```sql
USE zhengshiban_prod;
SHOW TABLES;
SELECT COUNT(*) FROM collect_category_mapping;  -- 应该返回51
SELECT * FROM admins;  -- 验证管理员账户
```

## 📊 数据库表结构

### 核心表统计
- **总表数**: 16个
- **用户相关**: 2个表
- **视频相关**: 4个表
- **采集系统**: 8个表
- **系统配置**: 2个表

### 主要表说明

#### 1. 用户表 (users)
- 存储用户基本信息
- 支持VIP功能
- 包含状态管理

#### 2. 视频表 (videos)
- 核心视频信息存储
- 支持短视频/长视频分类
- 包含采集来源信息
- 完整的统计字段 (观看、点赞、评论等)
- 支持VIP、付费、私有等功能

#### 3. 视频处理状态表 (video_processing_status)
- 跟踪视频处理进度
- 支持多种处理类型 (上传、转码、审核、缩略图)
- 包含错误处理和进度跟踪

#### 4. 采集系统表
- **collect_sources**: 采集源配置
- **collect_tasks**: 采集任务管理
- **collect_category_mapping**: 分类映射
- **collect_duplicates**: 重复检测
- **collect_logs**: 采集日志

## 🔧 配置说明

### 默认账户
- **管理员**: admin / password
- **系统用户**: system (用于采集等系统操作)

### 采集配置
- **360资源网**: 已配置完整的51个分类映射
- **支持格式**: XML API
- **采集间隔**: 3小时
- **超时设置**: 30秒

### 系统设置
- **上传限制**: 100MB
- **支持格式**: mp4,avi,mov,wmv,flv,mkv
- **默认状态**: 已发布、已审核
- **采集功能**: 已启用

## ⚠️ 注意事项

1. **字符编码**: 确保数据库和连接都使用utf8mb4
2. **外键约束**: 已设置完整的外键关系，删除时注意级联
3. **索引优化**: 已为常用查询字段添加索引
4. **密码安全**: 生产环境请修改默认密码
5. **权限设置**: 建议为应用创建专用数据库用户

## 🔄 更新记录

- **2025-07-25**: 初始版本，包含完整的采集系统和视频管理功能
- 添加了所有必需的字段以支持前端功能
- 配置了360资源网的完整分类映射
- 优化了数据库结构和索引

## 📞 技术支持

如有问题，请检查：
1. MySQL版本兼容性
2. 字符集设置
3. 权限配置
4. 外键约束设置
