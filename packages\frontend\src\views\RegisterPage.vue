<template>
  <div class="register-page">
    <div class="register-container">
      <div class="register-header">
        <h1>用户注册</h1>
        <p>创建您的新账户</p>
      </div>

      <form @submit.prevent="handleRegister" class="register-form">
        <div class="form-group">
          <label for="username">用户名</label>
          <input
            id="username"
            v-model="registerForm.username"
            type="text"
            placeholder="请输入用户名（3-20个字符）"
            required
            :disabled="loading"
          />
        </div>

        <div class="form-group">
          <label for="email">邮箱</label>
          <input
            id="email"
            v-model="registerForm.email"
            type="email"
            placeholder="请输入邮箱地址"
            required
            :disabled="loading"
          />
        </div>

        <div class="form-group">
          <label for="nickname">昵称（可选）</label>
          <input
            id="nickname"
            v-model="registerForm.nickname"
            type="text"
            placeholder="请输入昵称"
            :disabled="loading"
          />
        </div>

        <div class="form-group">
          <label for="password">密码</label>
          <input
            id="password"
            v-model="registerForm.password"
            type="password"
            placeholder="请输入密码（6-20个字符）"
            required
            :disabled="loading"
          />
        </div>

        <div class="form-group">
          <label for="confirmPassword">确认密码</label>
          <input
            id="confirmPassword"
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="请再次输入密码"
            required
            :disabled="loading"
          />
        </div>

        <div v-if="error" class="error-message">
          {{ error }}
        </div>

        <button
          type="submit"
          class="register-button"
          :disabled="loading"
          :class="{ loading }"
        >
          <span v-if="loading" class="loading-spinner"></span>
          {{ loading ? '注册中...' : '立即注册' }}
        </button>

        <div class="login-link">
          已有账号？
          <router-link to="/login">立即登录</router-link>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useGlobalStore } from '@/stores/global'

const router = useRouter()
const userStore = useUserStore()
const globalStore = useGlobalStore()

// 表单数据
const registerForm = reactive({
  username: '',
  email: '',
  nickname: '',
  password: '',
  confirmPassword: ''
})

// 状态
const loading = ref(false)
const error = ref('')

// 表单验证
const validateForm = () => {
  if (!registerForm.username || registerForm.username.length < 3 || registerForm.username.length > 20) {
    error.value = '用户名长度必须为3-20个字符'
    return false
  }

  if (!/^[a-zA-Z0-9]+$/.test(registerForm.username)) {
    error.value = '用户名只能包含字母和数字'
    return false
  }

  if (!registerForm.email) {
    error.value = '请输入邮箱地址'
    return false
  }

  if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(registerForm.email)) {
    error.value = '邮箱格式不正确'
    return false
  }

  if (!registerForm.password || registerForm.password.length < 6 || registerForm.password.length > 20) {
    error.value = '密码长度必须为6-20个字符'
    return false
  }

  if (registerForm.password !== registerForm.confirmPassword) {
    error.value = '两次密码输入不一致'
    return false
  }

  return true
}

// 处理注册
const handleRegister = async () => {
  error.value = ''

  if (!validateForm()) {
    return
  }

  loading.value = true

  try {
    const result = await userStore.register({
      username: registerForm.username,
      email: registerForm.email,
      password: registerForm.password,
      confirmPassword: registerForm.confirmPassword,
      nickname: registerForm.nickname || registerForm.username
    })

    if (result.success) {
      // 显示注册成功提示
      globalStore.addNotification({
        type: 'success',
        title: '注册成功',
        message: '账户创建成功，3秒后自动跳转到登录页面...'
      })

      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        router.push('/login')
      }, 3000)
    } else {
      // 根据不同的错误类型显示友好的提示
      const message = result.error || result.message || '注册失败'

      if (message.includes('用户名已存在')) {
        error.value = '该用户名已被使用，请选择其他用户名'
      } else if (message.includes('邮箱已存在')) {
        error.value = '该邮箱已被注册，请使用其他邮箱或直接登录'
      } else if (message.includes('密码')) {
        error.value = '密码格式不符合要求，请检查后重试'
      } else {
        error.value = message
      }
    }

  } catch (err: any) {
    error.value = err.message || '注册失败，请稍后重试'
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.register-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.register-container {
  background: white;
  border-radius: 12px;
  padding: 40px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.register-header h1 {
  color: #333;
  margin-bottom: 8px;
  font-size: 28px;
  font-weight: 600;
}

.register-header p {
  color: #666;
  font-size: 14px;
}

.register-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 8px;
  color: #333;
  font-weight: 500;
  font-size: 14px;
}

.form-group input {
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #667eea;
}

.error-message {
  color: #e74c3c;
  font-size: 14px;
  text-align: center;
  padding: 10px;
  background: #fdf2f2;
  border-radius: 6px;
  border: 1px solid #fecaca;
}

.register-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 14px 20px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.register-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.register-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.login-link {
  text-align: center;
  color: #666;
  font-size: 14px;
}

.login-link a {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.login-link a:hover {
  text-decoration: underline;
}
</style>
