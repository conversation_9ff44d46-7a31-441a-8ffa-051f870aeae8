<?php
declare(strict_types=1);

namespace app\service;

use think\facade\Log;
use think\facade\Cache;

/**
 * 企业级工具服务 - 重构优化版
 *
 * 功能特性：
 * - 统一工具方法管理
 * - 字符串处理工具
 * - 数组处理工具
 * - 时间处理工具
 * - 文件处理工具
 * - 加密解密工具
 * - 格式化工具
 *
 * <AUTHOR> Platform Team
 * @version 2.1
 */
class UtilityService
{
    /**
     * 字符串工具类
     */
    public static function str(): StringUtility
    {
        return new StringUtility();
    }

    /**
     * 数组工具类
     */
    public static function arr(): ArrayUtility
    {
        return new ArrayUtility();
    }

    /**
     * 时间工具类
     */
    public static function time(): TimeUtility
    {
        return new TimeUtility();
    }

    /**
     * 文件工具类
     */
    public static function file(): FileUtility
    {
        return new FileUtility();
    }

    /**
     * 加密工具类
     */
    public static function crypto(): CryptoUtility
    {
        return new CryptoUtility();
    }

    /**
     * 格式化工具类
     */
    public static function format(): FormatUtility
    {
        return new FormatUtility();
    }
}

/**
 * 字符串工具类
 */
class StringUtility
{
    /**
     * 生成随机字符串
     */
    public function random(int $length = 16, string $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'): string
    {
        $result = '';
        $charsLength = strlen($chars);

        for ($i = 0; $i < $length; $i++) {
            $result .= $chars[random_int(0, $charsLength - 1)];
        }

        return $result;
    }

    /**
     * 生成UUID
     */
    public function uuid(): string
    {
        return sprintf(
            '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            random_int(0, 0xffff), random_int(0, 0xffff),
            random_int(0, 0xffff),
            random_int(0, 0x0fff) | 0x4000,
            random_int(0, 0x3fff) | 0x8000,
            random_int(0, 0xffff), random_int(0, 0xffff), random_int(0, 0xffff)
        );
    }

    /**
     * 截取字符串并添加省略号
     */
    public function truncate(string $text, int $length = 100, string $suffix = '...'): string
    {
        if (mb_strlen($text) <= $length) {
            return $text;
        }

        return mb_substr($text, 0, $length) . $suffix;
    }

    /**
     * 驼峰转下划线
     */
    public function camelToSnake(string $input): string
    {
        return strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $input));
    }

    /**
     * 下划线转驼峰
     */
    public function snakeToCamel(string $input, bool $capitalizeFirst = false): string
    {
        $result = str_replace('_', '', ucwords($input, '_'));

        if (!$capitalizeFirst) {
            $result = lcfirst($result);
        }

        return $result;
    }

    /**
     * 清理HTML标签
     */
    public function stripTags(string $text, array $allowedTags = []): string
    {
        if (empty($allowedTags)) {
            return strip_tags($text);
        }

        $allowed = '<' . implode('><', $allowedTags) . '>';
        return strip_tags($text, $allowed);
    }

    /**
     * 高亮关键词
     */
    public function highlight(string $text, string $keyword, string $class = 'highlight'): string
    {
        if (empty($keyword)) {
            return $text;
        }

        return preg_replace(
            '/(' . preg_quote($keyword, '/') . ')/i',
            '<span class="' . $class . '">$1</span>',
            $text
        );
    }

    /**
     * 检查是否为有效的邮箱
     */
    public function isEmail(string $email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    /**
     * 检查是否为有效的URL
     */
    public function isUrl(string $url): bool
    {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }

    /**
     * 检查是否为有效的手机号
     */
    public function isMobile(string $mobile): bool
    {
        return preg_match('/^1[3-9]\d{9}$/', $mobile) === 1;
    }
}

/**
 * 数组工具类
 */
class ArrayUtility
{
    /**
     * 数组转树形结构
     */
    public function toTree(array $data, string $idKey = 'id', string $parentKey = 'parent_id', string $childrenKey = 'children'): array
    {
        $tree = [];
        $indexed = [];

        // 建立索引
        foreach ($data as $item) {
            $indexed[$item[$idKey]] = $item;
            $indexed[$item[$idKey]][$childrenKey] = [];
        }

        // 构建树形结构
        foreach ($indexed as $item) {
            if (isset($indexed[$item[$parentKey]])) {
                $indexed[$item[$parentKey]][$childrenKey][] = &$indexed[$item[$idKey]];
            } else {
                $tree[] = &$indexed[$item[$idKey]];
            }
        }

        return $tree;
    }

    /**
     * 树形结构转数组
     */
    public function fromTree(array $tree, string $childrenKey = 'children'): array
    {
        $result = [];

        foreach ($tree as $item) {
            $children = $item[$childrenKey] ?? [];
            unset($item[$childrenKey]);

            $result[] = $item;

            if (!empty($children)) {
                $result = array_merge($result, $this->fromTree($children, $childrenKey));
            }
        }

        return $result;
    }

    /**
     * 数组分组
     */
    public function groupBy(array $data, string $key): array
    {
        $grouped = [];

        foreach ($data as $item) {
            $groupKey = $item[$key] ?? 'unknown';
            $grouped[$groupKey][] = $item;
        }

        return $grouped;
    }

    /**
     * 数组去重（基于指定字段）
     */
    public function uniqueBy(array $data, string $key): array
    {
        $seen = [];
        $result = [];

        foreach ($data as $item) {
            $value = $item[$key] ?? null;

            if (!in_array($value, $seen)) {
                $seen[] = $value;
                $result[] = $item;
            }
        }

        return $result;
    }

    /**
     * 数组排序（支持多字段）
     */
    public function sortBy(array $data, array $sorts): array
    {
        usort($data, function($a, $b) use ($sorts) {
            foreach ($sorts as $field => $direction) {
                $aVal = $a[$field] ?? null;
                $bVal = $b[$field] ?? null;

                if ($aVal == $bVal) {
                    continue;
                }

                $result = $aVal <=> $bVal;

                if (strtolower($direction) === 'desc') {
                    $result = -$result;
                }

                return $result;
            }

            return 0;
        });

        return $data;
    }

    /**
     * 数组分页
     */
    public function paginate(array $data, int $page = 1, int $limit = 20): array
    {
        $total = count($data);
        $offset = ($page - 1) * $limit;
        $items = array_slice($data, $offset, $limit);

        return [
            'data' => $items,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ];
    }
}

/**
 * 时间工具类
 */
class TimeUtility
{
    /**
     * 格式化时间差
     */
    public function diffForHumans(string $datetime): string
    {
        $time = strtotime($datetime);
        $now = time();
        $diff = $now - $time;

        if ($diff < 60) {
            return '刚刚';
        } elseif ($diff < 3600) {
            return floor($diff / 60) . '分钟前';
        } elseif ($diff < 86400) {
            return floor($diff / 3600) . '小时前';
        } elseif ($diff < 2592000) {
            return floor($diff / 86400) . '天前';
        } elseif ($diff < 31536000) {
            return floor($diff / 2592000) . '个月前';
        } else {
            return floor($diff / 31536000) . '年前';
        }
    }

    /**
     * 格式化时长（秒转为时分秒）
     */
    public function formatDuration(int $seconds): string
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $seconds = $seconds % 60;

        if ($hours > 0) {
            return sprintf('%02d:%02d:%02d', $hours, $minutes, $seconds);
        } else {
            return sprintf('%02d:%02d', $minutes, $seconds);
        }
    }

    /**
     * 获取时间范围
     */
    public function getDateRange(string $period): array
    {
        $now = time();

        switch ($period) {
            case 'today':
                $start = strtotime('today');
                $end = strtotime('tomorrow') - 1;
                break;
            case 'yesterday':
                $start = strtotime('yesterday');
                $end = strtotime('today') - 1;
                break;
            case 'week':
                $start = strtotime('monday this week');
                $end = strtotime('sunday this week') + 86399;
                break;
            case 'month':
                $start = strtotime('first day of this month');
                $end = strtotime('last day of this month') + 86399;
                break;
            case 'year':
                $start = strtotime('first day of january this year');
                $end = strtotime('last day of december this year') + 86399;
                break;
            default:
                $start = $now;
                $end = $now;
        }

        return [
            'start' => date('Y-m-d H:i:s', $start),
            'end' => date('Y-m-d H:i:s', $end),
            'start_timestamp' => $start,
            'end_timestamp' => $end
        ];
    }
}

/**
 * 文件工具类
 */
class FileUtility
{
    /**
     * 格式化文件大小
     */
    public function formatSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $factor = floor((strlen((string)$bytes) - 1) / 3);

        return sprintf("%.2f %s", $bytes / pow(1024, $factor), $units[$factor]);
    }

    /**
     * 获取文件扩展名
     */
    public function getExtension(string $filename): string
    {
        return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
    }

    /**
     * 检查文件类型
     */
    public function isImage(string $filename): bool
    {
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
        return in_array($this->getExtension($filename), $imageExtensions);
    }

    /**
     * 检查是否为视频文件
     */
    public function isVideo(string $filename): bool
    {
        $videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'];
        return in_array($this->getExtension($filename), $videoExtensions);
    }

    /**
     * 生成安全的文件名
     */
    public function sanitizeFilename(string $filename): string
    {
        // 移除危险字符
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $filename);

        // 限制长度
        if (strlen($filename) > 255) {
            $extension = $this->getExtension($filename);
            $name = substr($filename, 0, 255 - strlen($extension) - 1);
            $filename = $name . '.' . $extension;
        }

        return $filename;
    }
}

/**
 * 加密工具类
 */
class CryptoUtility
{
    /**
     * 生成密码哈希
     */
    public function hashPassword(string $password): string
    {
        return password_hash($password, PASSWORD_DEFAULT);
    }

    /**
     * 验证密码
     */
    public function verifyPassword(string $password, string $hash): bool
    {
        return password_verify($password, $hash);
    }

    /**
     * 生成随机令牌
     */
    public function generateToken(int $length = 32): string
    {
        return bin2hex(random_bytes($length));
    }

    /**
     * 简单加密
     */
    public function encrypt(string $data, string $key): string
    {
        $cipher = 'AES-256-CBC';
        $iv = random_bytes(openssl_cipher_iv_length($cipher));
        $encrypted = openssl_encrypt($data, $cipher, $key, 0, $iv);

        return base64_encode($iv . $encrypted);
    }

    /**
     * 简单解密
     */
    public function decrypt(string $data, string $key): string
    {
        $cipher = 'AES-256-CBC';
        $data = base64_decode($data);
        $ivLength = openssl_cipher_iv_length($cipher);
        $iv = substr($data, 0, $ivLength);
        $encrypted = substr($data, $ivLength);

        return openssl_decrypt($encrypted, $cipher, $key, 0, $iv);
    }
}

/**
 * 格式化工具类
 */
class FormatUtility
{
    /**
     * 格式化数字
     */
    public function number(float $number, int $decimals = 2): string
    {
        return number_format($number, $decimals, '.', ',');
    }

    /**
     * 格式化百分比
     */
    public function percentage(float $value, int $decimals = 2): string
    {
        return $this->number($value * 100, $decimals) . '%';
    }

    /**
     * 格式化货币
     */
    public function currency(float $amount, string $currency = '¥'): string
    {
        return $currency . $this->number($amount, 2);
    }

    /**
     * 格式化手机号
     */
    public function phone(string $phone): string
    {
        if (strlen($phone) === 11) {
            return substr($phone, 0, 3) . '****' . substr($phone, -4);
        }

        return $phone;
    }

    /**
     * 格式化邮箱
     */
    public function email(string $email): string
    {
        $parts = explode('@', $email);
        if (count($parts) === 2) {
            $username = $parts[0];
            $domain = $parts[1];

            if (strlen($username) > 3) {
                $username = substr($username, 0, 3) . '***';
            }

            return $username . '@' . $domain;
        }

        return $email;
    }

    /**
     * 格式化JSON
     */
    public function json($data, bool $pretty = true): string
    {
        $flags = JSON_UNESCAPED_UNICODE;

        if ($pretty) {
            $flags |= JSON_PRETTY_PRINT;
        }

        return json_encode($data, $flags);
    }
}