# 采集模块数据库需求文档

## 📋 概述
本文档详细列出了视频采集模块所需的所有数据库表和字段，包括表之间的关联关系。

## 🗄️ 数据库表清单

### 1. 采集源配置表 (collect_sources)
**用途**: 存储采集源的基本配置信息
**关联**: 被多个表外键引用

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | 11 | 是 | AUTO_INCREMENT | 主键 |
| name | varchar | 100 | 是 | - | 采集源名称 |
| api_url | varchar | 500 | 是 | - | API地址 |
| api_type | enum | - | 是 | xml | API类型(json,xml,rss) |
| auth_key | varchar | 100 | 否 | NULL | 认证密钥 |
| status | tinyint | 1 | 是 | 1 | 状态(1启用,0禁用) |
| description | text | - | 否 | NULL | 描述 |
| charset | varchar | 20 | 否 | utf-8 | 编码格式 |
| user_agent | varchar | 500 | 否 | NULL | 用户代理 |
| headers | text | - | 否 | NULL | 请求头信息(JSON格式) |
| timeout | int | 11 | 否 | 30 | 超时时间(秒) |
| retry_times | tinyint | 3 | 否 | 3 | 重试次数 |
| collect_interval | int | 11 | 否 | 3 | 采集间隔(秒) |
| last_collect_time | timestamp | - | 否 | NULL | 最后采集时间 |
| total_collected | int | 11 | 否 | 0 | 总采集数量 |
| success_rate | decimal | 5,2 | 否 | 0.00 | 成功率 |
| created_at | timestamp | - | 否 | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | timestamp | - | 否 | CURRENT_TIMESTAMP | 更新时间 |

### 2. 分类映射表 (collect_category_mapping)
**用途**: 映射远程分类到本地分类
**关联**: 外键关联 collect_sources 和 video_categories

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | 11 | 是 | AUTO_INCREMENT | 主键 |
| source_id | int | 11 | 是 | - | 采集源ID(外键) |
| remote_category_id | varchar | 50 | 是 | - | 远程分类ID |
| remote_category_name | varchar | 100 | 是 | - | 远程分类名称 |
| local_category_id | int | 11 | 是 | - | 本地分类ID(外键) |
| created_at | timestamp | - | 否 | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | timestamp | - | 否 | CURRENT_TIMESTAMP | 更新时间 |

### 3. 采集任务表 (collect_tasks)
**用途**: 存储采集任务信息
**关联**: 外键关联 collect_sources

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | 11 | 是 | AUTO_INCREMENT | 主键 |
| source_id | int | 11 | 是 | - | 采集源ID(外键) |
| task_name | varchar | 100 | 是 | - | 任务名称 |
| task_type | enum | - | 是 | video | 任务类型(category,video,update,detail) |
| collect_type | enum | - | 是 | all | 采集方式(all,increment,update,detail) |
| category_filter | text | - | 否 | NULL | 分类过滤(JSON格式) |
| keyword_filter | varchar | 200 | 否 | NULL | 关键词过滤 |
| exclude_keywords | varchar | 500 | 否 | NULL | 排除关键词 |
| status | enum | - | 是 | pending | 状态(pending,running,completed,failed,paused,stopped) |
| progress | tinyint | 3 | 是 | 0 | 进度(0-100) |
| total_count | int | 11 | 否 | 0 | 总数量 |
| processed_count | int | 11 | 否 | 0 | 已处理数量 |
| success_count | int | 11 | 否 | 0 | 成功数量 |
| failed_count | int | 11 | 否 | 0 | 失败数量 |
| config | text | - | 否 | NULL | 任务配置(JSON格式) |
| start_time | timestamp | - | 否 | NULL | 开始时间 |
| end_time | timestamp | - | 否 | NULL | 结束时间 |
| created_at | timestamp | - | 否 | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | timestamp | - | 否 | CURRENT_TIMESTAMP | 更新时间 |

### 4. 采集日志表 (collect_logs)
**用途**: 记录采集过程中的详细日志
**关联**: 外键关联 collect_tasks, collect_sources, videos

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | 11 | 是 | AUTO_INCREMENT | 主键 |
| task_id | int | 11 | 是 | - | 任务ID(外键) |
| source_id | int | 11 | 是 | - | 采集源ID(外键) |
| video_id | int | 11 | 否 | NULL | 视频ID(外键) |
| remote_video_id | varchar | 50 | 否 | NULL | 远程视频ID |
| action | enum | - | 是 | - | 操作类型(create,update,skip,error) |
| message | text | - | 否 | NULL | 日志信息 |
| data | text | - | 否 | NULL | 相关数据(JSON格式) |
| created_at | timestamp | - | 否 | CURRENT_TIMESTAMP | 创建时间 |

### 5. 采集去重表 (collect_duplicates)
**用途**: 防止重复采集相同内容
**关联**: 外键关联 collect_sources, videos

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | 11 | 是 | AUTO_INCREMENT | 主键 |
| source_id | int | 11 | 是 | - | 采集源ID(外键) |
| remote_video_id | varchar | 50 | 是 | - | 远程视频ID |
| video_id | int | 11 | 是 | - | 本地视频ID(外键) |
| title_hash | varchar | 32 | 是 | - | 标题哈希 |
| created_at | timestamp | - | 否 | CURRENT_TIMESTAMP | 创建时间 |

### 6. 采集绑定表 (collect_bind)
**用途**: 绑定本地视频与远程视频的关系
**关联**: 外键关联 collect_sources, videos

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | 11 | 是 | AUTO_INCREMENT | 主键 |
| source_id | int | 11 | 是 | - | 采集源ID(外键) |
| local_video_id | int | 11 | 是 | - | 本地视频ID(外键) |
| remote_video_id | varchar | 50 | 是 | - | 远程视频ID |
| bind_time | timestamp | - | 是 | CURRENT_TIMESTAMP | 绑定时间 |
| last_update | timestamp | - | 否 | NULL | 最后更新时间 |
| update_count | int | 11 | 是 | 0 | 更新次数 |

### 7. 采集统计表 (collect_statistics)
**用途**: 存储采集统计数据
**关联**: 外键关联 collect_sources

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | 11 | 是 | AUTO_INCREMENT | 主键 |
| source_id | int | 11 | 是 | - | 采集源ID(外键) |
| date | date | - | 是 | - | 统计日期 |
| total_count | int | 11 | 是 | 0 | 总采集数 |
| success_count | int | 11 | 是 | 0 | 成功数 |
| failed_count | int | 11 | 是 | 0 | 失败数 |
| skip_count | int | 11 | 是 | 0 | 跳过数 |
| update_count | int | 11 | 是 | 0 | 更新数 |
| created_at | timestamp | - | 否 | CURRENT_TIMESTAMP | 创建时间 |

### 8. 采集配置表 (collect_config)
**用途**: 存储采集相关的配置参数
**关联**: 独立表，无外键关联

| 字段名 | 类型 | 长度 | 是否必填 | 默认值 | 说明 |
|--------|------|------|----------|--------|------|
| id | int | 11 | 是 | AUTO_INCREMENT | 主键 |
| config_key | varchar | 100 | 是 | - | 配置键名 |
| config_value | text | - | 否 | NULL | 配置值 |
| description | varchar | 255 | 否 | NULL | 配置描述 |
| created_at | timestamp | - | 否 | CURRENT_TIMESTAMP | 创建时间 |
| updated_at | timestamp | - | 否 | CURRENT_TIMESTAMP | 更新时间 |

## 🔗 表关联关系

### 主要外键关系
1. **collect_category_mapping** → collect_sources (source_id)
2. **collect_category_mapping** → video_categories (local_category_id)
3. **collect_tasks** → collect_sources (source_id)
4. **collect_logs** → collect_tasks (task_id)
5. **collect_logs** → collect_sources (source_id)
6. **collect_logs** → videos (video_id)
7. **collect_duplicates** → collect_sources (source_id)
8. **collect_duplicates** → videos (video_id)
9. **collect_bind** → collect_sources (source_id)
10. **collect_bind** → videos (local_video_id)
11. **collect_statistics** → collect_sources (source_id)

### 索引需求
- 所有外键字段需要建立索引
- 查询频繁的字段需要建立索引
- 唯一性约束字段需要建立唯一索引

## 📝 备注
- 所有表使用 InnoDB 存储引擎
- 字符集使用 utf8mb4
- 排序规则使用 utf8mb4_unicode_ci
- 时间字段统一使用 timestamp 类型
- JSON 数据存储在 text 字段中
