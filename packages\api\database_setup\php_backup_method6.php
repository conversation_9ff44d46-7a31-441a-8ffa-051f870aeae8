<?php
/**
 * PHP方式数据库备份 - 方法6
 * 确保编码正确，兼容性最强
 */

$host = 'shipin-mysql';
$username = 'root';
$password = 'root123456';
$database = 'zhengshiban_dev';
$backupFile = __DIR__ . '/backup_method6_php_export.sql';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]);

    $sql = "-- =============================================\n";
    $sql .= "-- PHP方式数据库备份 - 方法6\n";
    $sql .= "-- 生成时间: " . date('Y-m-d H:i:s') . "\n";
    $sql .= "-- 数据库: $database\n";
    $sql .= "-- 字符集: utf8mb4\n";
    $sql .= "-- =============================================\n\n";
    
    $sql .= "SET NAMES utf8mb4;\n";
    $sql .= "SET FOREIGN_KEY_CHECKS = 0;\n";
    $sql .= "SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO';\n";
    $sql .= "SET AUTOCOMMIT = 0;\n";
    $sql .= "START TRANSACTION;\n\n";

    // 获取所有表
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    
    foreach ($tables as $table) {
        echo "备份表: $table\n";
        
        // 表结构
        $createTable = $pdo->query("SHOW CREATE TABLE `$table`")->fetch(PDO::FETCH_ASSOC);
        $sql .= "-- 表结构: $table\n";
        $sql .= "DROP TABLE IF EXISTS `$table`;\n";
        $sql .= $createTable['Create Table'] . ";\n\n";
        
        // 表数据
        $stmt = $pdo->query("SELECT * FROM `$table`");
        $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($rows)) {
            $sql .= "-- 表数据: $table\n";
            $columns = array_keys($rows[0]);
            $columnList = '`' . implode('`, `', $columns) . '`';
            
            $sql .= "INSERT INTO `$table` ($columnList) VALUES\n";
            
            $values = [];
            foreach ($rows as $row) {
                $rowValues = [];
                foreach ($row as $value) {
                    if ($value === null) {
                        $rowValues[] = 'NULL';
                    } else {
                        $escaped = str_replace(
                            ["\\", "'", "\n", "\r", "\t"],
                            ["\\\\", "\\'", "\\n", "\\r", "\\t"],
                            $value
                        );
                        $rowValues[] = "'$escaped'";
                    }
                }
                $values[] = '(' . implode(', ', $rowValues) . ')';
            }
            
            $sql .= implode(",\n", $values) . ";\n\n";
        } else {
            $sql .= "-- 表 $table 无数据\n\n";
        }
    }
    
    $sql .= "COMMIT;\n";
    $sql .= "SET FOREIGN_KEY_CHECKS = 1;\n";
    
    file_put_contents($backupFile, $sql);
    echo "PHP备份完成: $backupFile\n";
    echo "文件大小: " . round(filesize($backupFile) / 1024 / 1024, 2) . " MB\n";
    
} catch (Exception $e) {
    echo "PHP备份失败: " . $e->getMessage() . "\n";
}
?>
