<?php
/**
 * 完整数据库恢复主脚本
 * 运行所有的恢复脚本并插入基础数据
 */

echo "🚀 开始完整数据库恢复...\n";
echo "================================\n\n";

// 1. 运行基础表创建
echo "📋 第一步：创建基础表结构\n";
include '/var/www/html/database_setup/complete_restore.php';
echo "\n";

// 2. 运行扩展表创建
echo "📋 第二步：创建扩展表结构\n";
include '/var/www/html/database_setup/complete_restore_part2.php';
echo "\n";

// 3. 运行处理相关表创建
echo "📋 第三步：创建处理相关表结构\n";
include '/var/www/html/database_setup/complete_restore_part3.php';
echo "\n";

// 4. 插入基础数据
echo "📋 第四步：插入基础数据\n";

$host = 'mysql';
$username = 'root';
$password = 'root123456';
$database = 'zhengshiban_dev';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]);
    
    // 插入管理员数据
    echo "插入管理员数据...\n";
    $pdo->exec("
        INSERT INTO `admins` (`id`, `username`, `email`, `password`, `name`, `role_id`, `status`, `created_at`, `updated_at`) VALUES 
        (1, 'admin', '<EMAIL>', '\$2y\$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '超级管理员', 1, 'active', '2025-07-25 14:00:00', '2025-07-25 14:00:00')
        ON DUPLICATE KEY UPDATE updated_at = VALUES(updated_at)
    ");
    
    // 插入测试用户数据
    echo "插入用户数据...\n";
    $pdo->exec("
        INSERT INTO `users` (`id`, `username`, `email`, `password`, `nickname`, `status`, `created_at`, `updated_at`) VALUES 
        (1, 'testuser', '<EMAIL>', '\$2y\$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '测试用户', 'active', '2025-07-25 14:00:00', '2025-07-25 14:00:00'),
        (2, 'user2', '<EMAIL>', '\$2y\$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '用户2', 'active', '2025-07-25 15:00:00', '2025-07-25 15:00:00')
        ON DUPLICATE KEY UPDATE updated_at = VALUES(updated_at)
    ");
    
    // 插入视频分类数据
    echo "插入视频分类数据...\n";
    $pdo->exec("
        INSERT INTO `video_categories` (`id`, `name`, `description`, `video_type`, `parent_id`, `sort_order`, `status`, `created_at`, `updated_at`) VALUES 
        (1, '搞笑', '搞笑短视频', 'short', 0, 1, 1, '2025-07-25 14:00:00', '2025-07-25 14:00:00'),
        (2, '娱乐', '娱乐短视频', 'short', 0, 2, 1, '2025-07-25 14:00:00', '2025-07-25 14:00:00'),
        (3, '生活', '生活短视频', 'short', 0, 3, 1, '2025-07-25 14:00:00', '2025-07-25 14:00:00'),
        (4, '美食', '美食短视频', 'short', 0, 4, 1, '2025-07-25 14:00:00', '2025-07-25 14:00:00'),
        (5, '电影', '电影长视频', 'long', 0, 1, 1, '2025-07-25 14:00:00', '2025-07-25 14:00:00'),
        (6, '电视剧', '电视剧长视频', 'long', 0, 2, 1, '2025-07-25 14:00:00', '2025-07-25 14:00:00'),
        (7, '纪录片', '纪录片长视频', 'long', 0, 3, 1, '2025-07-25 14:00:00', '2025-07-25 14:00:00'),
        (8, '综艺', '综艺长视频', 'long', 0, 4, 1, '2025-07-25 14:00:00', '2025-07-25 14:00:00')
        ON DUPLICATE KEY UPDATE updated_at = VALUES(updated_at)
    ");
    
    // 插入通用分类数据
    echo "插入通用分类数据...\n";
    $pdo->exec("
        INSERT INTO `categories` (`id`, `name`, `slug`, `description`, `parent_id`, `level`, `sort_order`, `status`, `created_at`, `updated_at`) VALUES 
        (1, '短视频', 'short-videos', '短视频分类', 0, 1, 1, 'active', '2025-07-25 14:00:00', '2025-07-25 14:00:00'),
        (2, '长视频', 'long-videos', '长视频分类', 0, 1, 2, 'active', '2025-07-25 14:00:00', '2025-07-25 14:00:00'),
        (3, '直播', 'live-videos', '直播分类', 0, 1, 3, 'active', '2025-07-25 14:00:00', '2025-07-25 14:00:00')
        ON DUPLICATE KEY UPDATE updated_at = VALUES(updated_at)
    ");
    
    // 插入示例视频数据
    echo "插入示例视频数据...\n";
    $pdo->exec("
        INSERT INTO `videos` (`id`, `title`, `description`, `cover_image`, `file_path`, `video_type`, `category_id`, `user_id`, `view_count`, `like_count`, `comment_count`, `status`, `audit_status`, `created_at`, `updated_at`) VALUES 
        (1, '搞笑短视频1', '这是一个搞笑的短视频', '/uploads/covers/funny1.jpg', '/uploads/videos/funny1.mp4', 'short', 1, 1, 150, 25, 8, 'published', 'approved', '2025-07-25 14:00:00', '2025-07-25 14:00:00'),
        (2, '娱乐短视频2', '娱乐内容短视频', '/uploads/covers/entertainment2.jpg', '/uploads/videos/entertainment2.mp4', 'short', 2, 1, 200, 30, 12, 'published', 'approved', '2025-07-25 15:00:00', '2025-07-25 15:00:00'),
        (3, '生活短视频3', '生活记录短视频', '/uploads/covers/life3.jpg', '/uploads/videos/life3.mp4', 'short', 3, 2, 100, 15, 5, 'published', 'approved', '2025-07-25 16:00:00', '2025-07-25 16:00:00'),
        (4, '美食短视频4', '美食制作短视频', '/uploads/covers/food4.jpg', '/uploads/videos/food4.mp4', 'short', 4, 2, 300, 45, 20, 'published', 'approved', '2025-07-25 17:00:00', '2025-07-25 17:00:00'),
        (5, '电影长视频1', '经典电影长视频', '/uploads/covers/movie1.jpg', '/uploads/videos/movie1.mp4', 'long', 5, 1, 1000, 100, 50, 'published', 'approved', '2025-07-25 18:00:00', '2025-07-25 18:00:00')
        ON DUPLICATE KEY UPDATE updated_at = VALUES(updated_at)
    ");
    
    // 插入系统配置数据
    echo "插入系统配置数据...\n";
    $pdo->exec("
        INSERT INTO `system_configs` (`config_key`, `config_value`, `data_type`, `config_group`, `title`, `description`, `is_public`, `sort_order`) VALUES
        ('site_name', '正式版短视频平台', 'string', 'basic', '网站名称', '网站的名称', 1, 1),
        ('site_description', '专业的短视频分享平台', 'string', 'basic', '网站描述', '网站的描述信息', 1, 2),
        ('upload_max_size', '100', 'int', 'upload', '最大上传大小', '单个文件最大上传大小(MB)', 0, 1),
        ('video_max_duration', '300', 'int', 'video', '短视频最大时长', '短视频最大时长(秒)', 0, 1),
        ('enable_audit', '1', 'bool', 'audit', '启用审核', '是否启用视频审核功能', 0, 1)
        ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP
    ");
    
    echo "✅ 基础数据插入完成\n\n";
    
} catch (Exception $e) {
    echo "❌ 数据插入失败: " . $e->getMessage() . "\n";
    exit(1);
}

// 5. 验证恢复结果
echo "📋 第五步：验证恢复结果\n";

try {
    // 获取所有表
    $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
    echo "数据库表总数: " . count($tables) . "\n";
    
    // 显示每个表的记录数
    echo "\n各表记录统计:\n";
    foreach ($tables as $table) {
        $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
        echo sprintf("%-25s: %d 条记录\n", $table, $count);
    }
    
    echo "\n🎉 完整数据库恢复成功！\n";
    echo "================================\n";
    echo "管理员账户: admin / password\n";
    echo "测试用户: testuser / password\n";
    echo "数据库已包含完整的表结构和基础数据\n";
    
} catch (Exception $e) {
    echo "❌ 验证失败: " . $e->getMessage() . "\n";
}
?>
