-- 重新创建基础数据表
USE zhengshiban_dev;

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `gender` enum('male','female','other') DEFAULT 'other' COMMENT '性别',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `bio` text COMMENT '个人简介',
  `status` enum('active','inactive','banned') DEFAULT 'active' COMMENT '状态',
  `last_login_time` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 管理员表
CREATE TABLE IF NOT EXISTS `admins` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '管理员用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `role` enum('super_admin','admin','editor') DEFAULT 'admin' COMMENT '角色',
  `permissions` json DEFAULT NULL COMMENT '权限',
  `status` enum('active','inactive') DEFAULT 'active' COMMENT '状态',
  `last_login_time` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 视频分类表
CREATE TABLE IF NOT EXISTS `video_categories` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `description` text COMMENT '分类描述',
  `video_type` enum('short','long','live') NOT NULL DEFAULT 'short' COMMENT '视频类型',
  `parent_id` int unsigned DEFAULT 0 COMMENT '父分类ID',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `video_type` (`video_type`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 视频表
CREATE TABLE IF NOT EXISTS `videos` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL COMMENT '视频标题',
  `description` text COMMENT '视频描述',
  `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图片',
  `cover_image_backup` varchar(255) DEFAULT NULL COMMENT '备用封面图片',
  `file_path` varchar(255) DEFAULT NULL COMMENT '视频文件路径',
  `hls_url` varchar(255) DEFAULT NULL COMMENT 'HLS播放地址',
  `duration` int DEFAULT 0 COMMENT '视频时长（秒）',
  `file_size` bigint DEFAULT 0 COMMENT '文件大小（字节）',
  `video_type` enum('short','long','live') NOT NULL DEFAULT 'short' COMMENT '视频类型',
  `category_id` int unsigned DEFAULT NULL COMMENT '分类ID',
  `user_id` int unsigned NOT NULL COMMENT '用户ID',
  `view_count` int DEFAULT 0 COMMENT '观看次数',
  `like_count` int DEFAULT 0 COMMENT '点赞数',
  `comment_count` int DEFAULT 0 COMMENT '评论数',
  `share_count` int DEFAULT 0 COMMENT '分享数',
  `is_vip` tinyint(1) DEFAULT 0 COMMENT '是否VIP视频',
  `status` enum('draft','published','private','deleted') DEFAULT 'published' COMMENT '状态',
  `audit_status` enum('pending','approved','rejected') DEFAULT 'pending' COMMENT '审核状态',
  `audit_message` text COMMENT '审核信息',
  `tags` json DEFAULT NULL COMMENT '标签',
  `source_url` varchar(500) DEFAULT NULL COMMENT '原始来源URL',
  `collected_at` timestamp NULL DEFAULT NULL COMMENT '采集时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `category_id` (`category_id`),
  KEY `video_type` (`video_type`),
  KEY `status` (`status`),
  KEY `audit_status` (`audit_status`),
  KEY `created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 视频处理状态表
CREATE TABLE IF NOT EXISTS `video_processing_status` (
  `id` int unsigned NOT NULL AUTO_INCREMENT,
  `video_id` int unsigned NOT NULL COMMENT '视频ID',
  `process_type` enum('upload','thumbnail','transcode','audit') NOT NULL COMMENT '处理类型',
  `status` enum('pending','processing','completed','failed') DEFAULT 'pending' COMMENT '处理状态',
  `progress` int DEFAULT 0 COMMENT '处理进度(0-100)',
  `current_step` varchar(255) DEFAULT NULL COMMENT '当前步骤',
  `message` text COMMENT '处理信息',
  `error_message` text COMMENT '错误信息',
  `started_at` timestamp NULL DEFAULT NULL COMMENT '开始时间',
  `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `video_id` (`video_id`),
  KEY `process_type` (`process_type`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入默认管理员账户
INSERT INTO `admins` (`username`, `email`, `password`, `nickname`, `role`, `status`) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '超级管理员', 'super_admin', 'active')
ON DUPLICATE KEY UPDATE `updated_at` = CURRENT_TIMESTAMP;

-- 插入默认用户
INSERT INTO `users` (`username`, `email`, `password`, `nickname`, `status`) VALUES
('testuser', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '测试用户', 'active')
ON DUPLICATE KEY UPDATE `updated_at` = CURRENT_TIMESTAMP;

-- 插入默认分类
INSERT INTO `video_categories` (`name`, `description`, `video_type`, `sort_order`, `status`) VALUES
('搞笑', '搞笑短视频', 'short', 1, 1),
('娱乐', '娱乐短视频', 'short', 2, 1),
('生活', '生活短视频', 'short', 3, 1),
('美食', '美食短视频', 'short', 4, 1),
('电影', '电影长视频', 'long', 1, 1),
('电视剧', '电视剧长视频', 'long', 2, 1),
('纪录片', '纪录片长视频', 'long', 3, 1),
('综艺', '综艺长视频', 'long', 4, 1)
ON DUPLICATE KEY UPDATE `updated_at` = CURRENT_TIMESTAMP;
