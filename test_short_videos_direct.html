<!DOCTYPE html>
<html>
<head>
    <title>直接测试短视频API</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { white-space: pre-wrap; word-wrap: break-word; max-height: 400px; overflow-y: auto; }
        .video-item { border: 1px solid #ddd; margin: 10px 0; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>直接测试短视频API</h1>
    
    <button onclick="testShortVideosAPI()">测试短视频API</button>
    <button onclick="testWithoutAuth()">测试无认证API</button>
    
    <div id="result" class="result"></div>

    <script>
    async function testShortVideosAPI() {
        const resultDiv = document.getElementById('result');
        resultDiv.innerHTML = '测试中...';
        
        try {
            console.log('开始测试短视频API...');
            
            const url = 'http://localhost:3000/api/v1/videos?video_type=short&limit=10&page=1&sort=created_at&order=desc';
            console.log('请求URL:', url);
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': 'ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+='
                }
            });
            
            console.log('响应状态:', response.status);
            console.log('响应头:', [...response.headers.entries()]);
            
            const data = await response.json();
            console.log('响应数据:', data);
            
            resultDiv.className = 'result ' + (response.ok ? 'success' : 'error');
            
            let html = `
                <h3>API测试结果</h3>
                <p><strong>状态码:</strong> ${response.status}</p>
                <p><strong>成功:</strong> ${data.success ? '是' : '否'}</p>
                <p><strong>消息:</strong> ${data.message || 'N/A'}</p>
            `;
            
            if (data.success && data.data) {
                const videos = data.data.data || data.data.list || [];
                html += `
                    <p><strong>视频数量:</strong> ${videos.length}</p>
                    <h4>视频列表:</h4>
                `;
                
                videos.forEach((video, index) => {
                    html += `
                        <div class="video-item">
                            <p><strong>视频 ${index + 1}:</strong></p>
                            <p>ID: ${video.id}</p>
                            <p>标题: ${video.title}</p>
                            <p>类型: ${video.video_type}</p>
                            <p>状态: ${video.status}</p>
                            <p>审核状态: ${video.audit_status}</p>
                            <p>文件路径: ${video.file_path || 'N/A'}</p>
                            <p>HLS路径: ${video.hls_path || 'N/A'}</p>
                            <p>封面: ${video.cover_image || 'N/A'}</p>
                        </div>
                    `;
                });
            }
            
            html += `
                <h4>完整响应数据:</h4>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            
            resultDiv.innerHTML = html;
            
        } catch (error) {
            console.error('测试失败:', error);
            resultDiv.className = 'result error';
            resultDiv.innerHTML = `
                <h3>测试失败</h3>
                <p><strong>错误:</strong> ${error.message}</p>
                <pre>${error.stack || ''}</pre>
            `;
        }
    }
    
    async function testWithoutAuth() {
        const resultDiv = document.getElementById('result');
        resultDiv.innerHTML = '测试无认证API...';
        
        try {
            const response = await fetch('http://localhost:3000/api/v1/videos?video_type=short&limit=5', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            
            const data = await response.json();
            
            resultDiv.className = 'result error';
            resultDiv.innerHTML = `
                <h3>无认证测试结果</h3>
                <p><strong>状态码:</strong> ${response.status}</p>
                <p><strong>响应:</strong></p>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            
        } catch (error) {
            resultDiv.className = 'result error';
            resultDiv.innerHTML = `
                <h3>无认证测试失败</h3>
                <p><strong>错误:</strong> ${error.message}</p>
            `;
        }
    }
    </script>
</body>
</html>
