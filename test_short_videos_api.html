<!DOCTYPE html>
<html>
<head>
    <title>测试短视频API</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <h1>短视频API测试</h1>
    
    <div class="test-section">
        <h3>测试1: 获取所有短视频</h3>
        <button onclick="testAllShortVideos()">测试获取所有短视频</button>
        <div id="result1" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>测试2: 获取推荐短视频</h3>
        <button onclick="testRecommendShortVideos()">测试推荐短视频</button>
        <div id="result2" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>测试3: 获取分类短视频</h3>
        <button onclick="testCategoryShortVideos()">测试分类短视频</button>
        <div id="result3" class="result"></div>
    </div>
    
    <div class="test-section">
        <h3>测试4: 检查数据库数据</h3>
        <button onclick="testDatabaseData()">检查数据库短视频</button>
        <div id="result4" class="result"></div>
    </div>

    <script>
    async function testAllShortVideos() {
        const resultDiv = document.getElementById('result1');
        resultDiv.innerHTML = '测试中...';

        try {
            const response = await fetch('http://localhost:3000/api/v1/videos?video_type=short&limit=10', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': 'ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+='
                }
            });
            
            const data = await response.json();
            resultDiv.className = 'result ' + (response.ok ? 'success' : 'error');
            resultDiv.innerHTML = `
                <h4>响应状态: ${response.status}</h4>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
        } catch (error) {
            resultDiv.className = 'result error';
            resultDiv.innerHTML = `<h4>错误: ${error.message}</h4>`;
        }
    }
    
    async function testRecommendShortVideos() {
        const resultDiv = document.getElementById('result2');
        resultDiv.innerHTML = '测试中...';
        
        try {
            const response = await fetch('http://localhost:3000/api/v1/videos?video_type=short&sort=view_count&order=desc&limit=5', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': 'ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+='
                }
            });
            
            const data = await response.json();
            resultDiv.className = 'result ' + (response.ok ? 'success' : 'error');
            resultDiv.innerHTML = `
                <h4>响应状态: ${response.status}</h4>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
        } catch (error) {
            resultDiv.className = 'result error';
            resultDiv.innerHTML = `<h4>错误: ${error.message}</h4>`;
        }
    }
    
    async function testCategoryShortVideos() {
        const resultDiv = document.getElementById('result3');
        resultDiv.innerHTML = '测试中...';
        
        try {
            const response = await fetch('http://localhost:3000/api/v1/videos?video_type=short&category_id=1&limit=5', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': 'ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+='
                }
            });
            
            const data = await response.json();
            resultDiv.className = 'result ' + (response.ok ? 'success' : 'error');
            resultDiv.innerHTML = `
                <h4>响应状态: ${response.status}</h4>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
        } catch (error) {
            resultDiv.className = 'result error';
            resultDiv.innerHTML = `<h4>错误: ${error.message}</h4>`;
        }
    }
    
    async function testDatabaseData() {
        const resultDiv = document.getElementById('result4');
        resultDiv.innerHTML = '测试中...';
        
        try {
            // 这个接口可能需要管理员权限，我们先试试
            const response = await fetch('http://localhost:3000/api/admin/videos?video_type=short', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': 'ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+='
                }
            });
            
            const data = await response.json();
            resultDiv.className = 'result ' + (response.ok ? 'success' : 'error');
            resultDiv.innerHTML = `
                <h4>响应状态: ${response.status}</h4>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
        } catch (error) {
            resultDiv.className = 'result error';
            resultDiv.innerHTML = `<h4>错误: ${error.message}</h4>`;
        }
    }
    </script>
</body>
</html>
