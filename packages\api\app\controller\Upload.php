<?php
declare(strict_types=1);

namespace app\controller;

use think\Request;
use think\Response;
use think\facade\Filesystem;
use think\facade\Validate;
use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;
use app\service\ResponseService;
use app\service\ValidationService;
use app\service\UploadService;
use app\service\VideoProcessingService;
use app\service\VideoProcessingStatusService;

/**
 * 文件上传控制器
 */
class Upload
{
    protected $responseService;
    protected $validationService;
    protected $uploadService;
    protected $videoProcessingService;
    protected $videoProcessingStatusService;

    public function __construct()
    {
        $this->responseService = new ResponseService();
        $this->validationService = new ValidationService();
        $this->uploadService = new UploadService();
        $this->videoProcessingService = new VideoProcessingService();
        $this->videoProcessingStatusService = new VideoProcessingStatusService();
    }
    /**
     * 上传视频文件 - 使用安全上传服务
     */
    public function video(Request $request): Response
    {
        try {
            // 检查用户认证
            if (!isset($request->userInfo['id'])) {
                return $this->responseService->error('用户未认证', 401);
            }

            $file = $request->file('video');

            if (!$file) {
                return $this->responseService->error('请选择要上传的视频文件', 400);
            }

            // 记录上传请求信息 - 只记录关键信息
            Log::channel('upload')->info('视频上传开始', [
                'user_id' => $request->userInfo['id'],
                'file_name' => $file->getOriginalName(),
                'file_size' => round($file->getSize() / 1024 / 1024, 2) . 'MB'  // 简化文件大小显示
            ]);

            // 获取视频类型和分类
            $videoType = $request->param('video_type', 'long');
            $categoryId = $request->param('category_id');

            if (!$categoryId) {
                return $this->responseService->error('请选择视频分类', 400);
            }

            // 使用统一上传服务
            $result = $this->uploadService->upload($file, [
                'type' => 'video',
                'user_id' => $request->userInfo['id'],
                'security_level' => 'normal'
            ]);

            // 生成缩略图
            $coverImage = '';
            try {
                $absolutePath = root_path() . 'public' . $result['path'];
                $processDir = root_path() . 'runtime' . DIRECTORY_SEPARATOR . 'video_processing' . DIRECTORY_SEPARATOR . 'temp_' . time();
                $coverImage = $this->videoProcessingService->generateThumbnail($absolutePath, $processDir);
            } catch (\Exception $e) {
                Log::warning('缩略图生成失败', ['error' => $e->getMessage()]);
            }

            // 创建视频记录
            $videoData = [
                'title' => $this->generateTitleFromFilename($file->getOriginalName()),
                'description' => '',
                'video_type' => $videoType,
                'category_id' => $categoryId,
                'user_id' => $request->userInfo['id'],
                'file_path' => $result['path'],
                'cover_image' => $coverImage,
                'duration' => 0,
                'file_size' => $result['size'],
                'width' => 0,
                'height' => 0,
                'format' => pathinfo($file->getOriginalName(), PATHINFO_EXTENSION),
                'view_count' => 0,
                'like_count' => 0,
                'comment_count' => 0,
                'share_count' => 0,
                'collect_count' => 0,
                'completion_rate' => '0.00',
                'engagement_score' => '0.00',
                'is_featured' => 0,
                'is_private' => 0,
                'is_free' => 1,
                'points_price' => 0,
                'status' => 'uploading', // 上传状态，等待用户完善信息
                'audit_status' => 'pending',
                'published_at' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $videoId = Db::table('videos')->insertGetId($videoData);

            // 启动视频转码处理
            $this->triggerVideoProcessing($videoId, $result['path']);

            // 返回上传结果
            return $this->responseService->success([
                'video_id' => $videoId,
                'original_name' => $file->getOriginalName(),
                'filename' => $result['filename'],
                'file_path' => $result['path'],
                'file_size' => $result['size'],
                'mime_type' => $result['mime_type'],
                'title' => $videoData['title']
            ], '视频上传成功');

        } catch (\Exception $e) {
            return $this->responseService->error('视频上传失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 从文件名生成视频标题
     */
    private function generateTitleFromFilename(string $filename): string
    {
        // 去掉文件扩展名
        $title = pathinfo($filename, PATHINFO_FILENAME);

        // 替换常见的分隔符为空格
        $title = str_replace(['_', '-', '.'], ' ', $title);

        // 限制长度
        if (mb_strlen($title) > 100) {
            $title = mb_substr($title, 0, 100);
        }

        return $title ?: '未命名视频';
    }
    
    /**
     * 上传图片文件 - 使用安全上传服务
     */
    public function image(Request $request): Response
    {
        try {
            // 检查用户认证
            if (!isset($request->userInfo['id'])) {
                return $this->responseService->error('用户未认证', 401);
            }

            $file = $request->file('image');

            if (!$file) {
                return $this->responseService->error('请选择要上传的图片文件', 400);
            }

            // 使用统一上传服务
            $result = $this->uploadService->upload($file, [
                'type' => 'image',
                'user_id' => $request->userInfo['id'],
                'security_level' => 'normal'
            ]);

            // 返回上传结果
            return $this->responseService->success([
                'original_name' => $file->getOriginalName(),
                'filename' => $result['filename'],
                'file_path' => $result['path'],
                'file_size' => $result['size'],
                'mime_type' => $result['mime_type']
            ], '图片上传成功');

        } catch (\Exception $e) {
            return $this->responseService->error('图片上传失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 分片上传接口 - 高速上传
     */
    public function chunk(Request $request): Response
    {
        try {
            // 检查用户认证
            if (!isset($request->userInfo['id'])) {
                return $this->responseService->error('用户未认证', 401);
            }

            $chunk = $request->file('chunk');
            $chunkIndex = $request->param('chunkIndex');
            $totalChunks = $request->param('totalChunks');
            $fileName = $request->param('fileName');

            if (!$chunk || $chunkIndex === null || !$totalChunks || !$fileName) {
                return $this->responseService->error('分片上传参数不完整', 400);
            }

            // 创建临时目录存储分片
            $tempDir = runtime_path() . 'chunks' . DIRECTORY_SEPARATOR . md5($fileName . $request->userInfo['id']);
            if (!is_dir($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            // 保存分片
            $chunkPath = $tempDir . DIRECTORY_SEPARATOR . 'chunk_' . $chunkIndex;
            $chunk->move($tempDir, 'chunk_' . $chunkIndex);

            // 只记录关键分片信息，避免大量日志
            if ($chunkIndex % 10 == 0 || $chunkIndex == $totalChunks - 1) {
                Log::channel('upload')->info('分片上传进度', [
                    'user_id' => $request->userInfo['id'],
                    'file_name' => $fileName,
                    'progress' => round(($chunkIndex + 1) / $totalChunks * 100, 1) . '%'
                ]);
            }

            return $this->responseService->success([
                'chunk_index' => $chunkIndex,
                'uploaded' => true
            ], '分片上传成功');

        } catch (\Exception $e) {
            Log::channel('upload')->error('分片上传失败', [
                'error' => $e->getMessage(),
                'user_id' => $request->userInfo['id'] ?? null,
                'file_name' => $fileName ?? 'unknown'
            ]);
            return $this->responseService->error('分片上传失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 上传头像 - 使用安全上传服务
     */
    public function avatar(Request $request): Response
    {
        try {
            // 检查用户认证
            if (!isset($request->userInfo['id'])) {
                return $this->responseService->error('用户未认证', 401);
            }

            $file = $request->file('avatar');

            if (!$file) {
                return $this->responseService->error('请选择要上传的头像文件', 400);
            }

            // 使用统一上传服务
            $result = $this->uploadService->upload($file, [
                'type' => 'avatar',
                'user_id' => $request->userInfo['id'],
                'security_level' => 'high'
            ]);

            // 返回上传结果
            return $this->responseService->success([
                'original_name' => $file->getOriginalName(),
                'filename' => $result['filename'],
                'file_path' => $result['path'],
                'file_size' => $result['size'],
                'mime_type' => $result['mime_type']
            ], '头像上传成功');

        } catch (\Exception $e) {
            return $this->responseService->error('头像上传失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 合并分片接口 - 完成高速上传
     */
    public function merge(Request $request): Response
    {
        try {
            // 检查用户认证
            if (!isset($request->userInfo['id'])) {
                return $this->responseService->error('用户未认证', 401);
            }

            $fileName = $request->param('fileName');
            $totalChunks = $request->param('totalChunks');
            $videoType = $request->param('video_type', 'long');
            $categoryId = $request->param('category_id');

            if (!$fileName || !$totalChunks || !$categoryId) {
                return $this->responseService->error('合并参数不完整', 400);
            }

            $tempDir = runtime_path() . 'chunks' . DIRECTORY_SEPARATOR . md5($fileName . $request->userInfo['id']);

            if (!is_dir($tempDir)) {
                return $this->responseService->error('分片文件不存在', 400);
            }

            // 检查所有分片是否存在
            for ($i = 0; $i < $totalChunks; $i++) {
                $chunkPath = $tempDir . DIRECTORY_SEPARATOR . 'chunk_' . $i;
                if (!file_exists($chunkPath)) {
                    return $this->responseService->error('分片 ' . $i . ' 不存在', 400);
                }
            }

            // 生成最终文件名和路径
            $extension = pathinfo($fileName, PATHINFO_EXTENSION);
            $finalFileName = 'video_' . time() . '_' . bin2hex(random_bytes(8)) . '.' . $extension;
            $uploadPath = $this->getUploadPath('videos', $request->userInfo['id']);
            $this->ensureDirectoryExists($uploadPath);
            $finalPath = $uploadPath . DIRECTORY_SEPARATOR . $finalFileName;

            // 合并分片
            $finalFile = fopen($finalPath, 'wb');
            if (!$finalFile) {
                return $this->responseService->error('无法创建最终文件', 500);
            }

            $totalSize = 0;
            for ($i = 0; $i < $totalChunks; $i++) {
                $chunkPath = $tempDir . DIRECTORY_SEPARATOR . 'chunk_' . $i;
                $chunkData = file_get_contents($chunkPath);
                fwrite($finalFile, $chunkData);
                $totalSize += strlen($chunkData);
                unlink($chunkPath); // 删除分片
            }
            fclose($finalFile);

            // 删除临时目录
            rmdir($tempDir);

            // 创建视频记录
            $videoData = [
                'title' => $this->generateTitleFromFilename($fileName),
                'description' => '',
                'video_type' => $videoType,
                'category_id' => $categoryId,
                'user_id' => $request->userInfo['id'],
                'file_path' => $this->getRelativePath($finalPath),
                'cover_image' => '',
                'duration' => 0,
                'file_size' => $totalSize,
                'width' => 0,
                'height' => 0,
                'format' => $extension,
                'view_count' => 0,
                'like_count' => 0,
                'comment_count' => 0,
                'share_count' => 0,
                'collect_count' => 0,
                'completion_rate' => '0.00',
                'engagement_score' => '0.00',
                'is_featured' => 0,
                'is_private' => 0,
                'is_free' => 1,
                'points_price' => 0,
                'status' => 'uploading',
                'audit_status' => 'pending',
                'published_at' => null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $videoId = Db::table('videos')->insertGetId($videoData);

            Log::channel('upload')->info('高速上传完成', [
                'user_id' => $request->userInfo['id'],
                'video_id' => $videoId,
                'file_name' => $fileName,
                'total_size' => round($totalSize / 1024 / 1024, 2) . 'MB'
            ]);

            return $this->responseService->success([
                'video_id' => $videoId,
                'original_name' => $fileName,
                'filename' => $finalFileName,
                'file_path' => $this->getRelativePath($finalPath),
                'file_size' => $totalSize,
                'title' => $this->generateTitleFromFilename($fileName)
            ], '高速上传完成');

        } catch (\Exception $e) {
            Log::channel('upload')->error('分片合并失败', [
                'error' => $e->getMessage(),
                'user_id' => $request->userInfo['id'] ?? null
            ]);
            return $this->responseService->error('分片合并失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取上传路径
     */
    private function getUploadPath(string $type, int $userId): string
    {
        $baseUploadPath = app()->getRootPath() . 'public' . DIRECTORY_SEPARATOR . 'uploads';
        $userPath = $baseUploadPath . DIRECTORY_SEPARATOR . $type . DIRECTORY_SEPARATOR . date('Y') . DIRECTORY_SEPARATOR . date('m');
        return $userPath;
    }

    /**
     * 获取相对路径
     */
    private function getRelativePath(string $fullPath): string
    {
        $publicPath = app()->getRootPath() . 'public';
        return str_replace($publicPath, '', $fullPath);
    }

    /**
     * 确保目录存在
     */
    private function ensureDirectoryExists(string $path): void
    {
        if (!is_dir($path)) {
            mkdir($path, 0755, true);
        }
    }

    // =====================================================
    // 分片上传相关API接口
    // =====================================================

    /**
     * 初始化分片上传
     */
    public function initChunkedUpload(Request $request): Response
    {
        try {
            // 检查用户认证
            if (!isset($request->userInfo['id'])) {
                return $this->responseService->error('用户未认证', 401);
            }

            // 获取请求参数
            $filename = $request->param('filename');
            $fileSize = (int) $request->param('file_size');
            $fileHash = $request->param('file_hash');

            if (!$filename || !$fileSize || !$fileHash) {
                return $this->responseService->error('缺少必要参数', 400);
            }

            // 初始化分片上传
            $result = $this->uploadService->initChunkedUpload([
                'filename' => $filename,
                'filesize' => $fileSize,
                'md5' => $fileHash,
                'user_id' => $request->userInfo['id'],
                'type' => 'video'
            ]);

            return $this->responseService->success($result, '分片上传初始化成功');

        } catch (\Exception $e) {
            Log::error('分片上传初始化失败', [
                'user_id' => $request->userInfo['id'] ?? null,
                'error' => $e->getMessage()
            ]);
            return $this->responseService->error('初始化失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 上传分片 - [v20240719]安全加固
     */
    public function uploadChunk(Request $request): Response
    {
        try {
            // 检查用户认证
            if (!isset($request->userInfo['id'])) {
                return $this->responseService->error('用户未认证', 401);
            }

            // 获取请求参数
            $uploadId = $request->param('upload_id');
            $chunkIndex = (int) $request->param('chunk_index');
            $totalChunks = (int) $request->param('total_chunks');
            $chunkFile = $request->file('chunk');

            if (!$uploadId || $chunkIndex < 0 || !$chunkFile) {
                return $this->responseService->error('缺少必要参数', 400);
            }

            // [SECURITY] 分片索引校验 - 防止索引越界攻击
            if ($totalChunks > 0 && $chunkIndex >= $totalChunks) {
                Log::warning('Upload: 分片索引越界攻击', [
                    'user_id' => $request->userInfo['id'],
                    'upload_id' => $uploadId,
                    'chunk_index' => $chunkIndex,
                    'total_chunks' => $totalChunks,
                    'ip' => $request->ip()
                ]);
                return $this->responseService->error('无效的分片索引', 400);
            }

            // [SECURITY] 分片大小检查 - 防止超大分片攻击
            $maxChunkSize = 10 * 1024 * 1024; // 10MB
            if ($chunkFile->getSize() > $maxChunkSize) {
                return $this->responseService->error('分片大小超过限制', 400);
            }

            // [SECURITY] 简单锁机制 - 防止并发上传冲突
            $lockKey = "upload_lock:{$uploadId}:{$chunkIndex}";

            // 检查是否有锁
            if (Cache::get($lockKey)) {
                return $this->responseService->error('分片正在上传中，请稍后重试', 409);
            }

            // 设置锁（30秒过期）
            Cache::set($lockKey, time(), 30);

            try {
                // 执行上传操作
                $result = $this->uploadService->uploadChunk($chunkFile, [
                    'upload_id' => $uploadId,
                    'chunk_index' => $chunkIndex
                ]);
            } finally {
                // 释放锁
                Cache::delete($lockKey);
            }

            return $this->responseService->success($result, '分片上传成功');

        } catch (\Exception $e) {
            Log::error('分片上传失败', [
                'user_id' => $request->userInfo['id'] ?? null,
                'upload_id' => $request->param('upload_id'),
                'chunk_index' => $request->param('chunk_index'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->responseService->error('分片上传失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 合并分片
     */
    public function mergeChunks(Request $request): Response
    {
        try {
            // 检查用户认证
            if (!isset($request->userInfo['id'])) {
                return $this->responseService->error('用户未认证', 401);
            }

            // 获取请求参数
            $uploadId = $request->param('upload_id');

            if (!$uploadId) {
                return $this->responseService->error('缺少上传ID', 400);
            }

            // 合并分片
            $result = $this->uploadService->mergeChunks($uploadId);

            // 将合并结果存储到缓存中，供complete接口使用（1小时过期）
            Cache::set('merge_result_' . $uploadId, $result, 3600);

            Log::info('分片合并成功，结果已缓存', [
                'upload_id' => $uploadId,
                'cache_key' => 'merge_result_' . $uploadId
            ]);

            return $this->responseService->success($result, '文件合并成功');

        } catch (\Exception $e) {
            Log::error('分片合并失败', [
                'user_id' => $request->userInfo['id'] ?? null,
                'upload_id' => $request->param('upload_id'),
                'error' => $e->getMessage()
            ]);
            return $this->responseService->error('合并失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 获取上传进度
     */
    public function getUploadProgress(Request $request): Response
    {
        try {
            // 检查用户认证
            if (!isset($request->userInfo['id'])) {
                return $this->responseService->error('用户未认证', 401);
            }

            // 获取请求参数
            $uploadId = $request->param('upload_id');

            if (!$uploadId) {
                return $this->responseService->error('缺少上传ID', 400);
            }

            // 获取上传进度
            $result = $this->uploadService->getUploadProgress($uploadId);

            return $this->responseService->success($result, '获取进度成功');

        } catch (\Exception $e) {
            return $this->responseService->error('获取进度失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 分片上传完整流程 - 创建视频记录
     */
    public function completeChunkedUpload(Request $request): Response
    {
        try {
            Log::info('分片上传完成开始', [
                'method' => $request->method(),
                'url' => $request->url(),
                'user_info' => $request->userInfo ?? 'null',
                'all_params' => $request->param()
            ]);

            // 检查用户认证
            if (!isset($request->userInfo['id'])) {
                Log::error('用户未认证', ['userInfo' => $request->userInfo ?? 'null']);
                return $this->responseService->error('用户未认证', 401);
            }

            // 获取请求参数
            $uploadId = $request->param('upload_id');
            $videoType = $request->param('video_type', 'long');
            $categoryId = $request->param('category_id');
            $title = $request->param('title');

            Log::info('分片上传完成请求参数', [
                'upload_id' => $uploadId,
                'video_type' => $videoType,
                'category_id' => $categoryId,
                'title' => $title,
                'user_id' => $request->userInfo['id']
            ]);

            if (!$uploadId) {
                Log::error('分片上传完成参数验证失败', [
                    'upload_id' => $uploadId,
                    'category_id' => $categoryId
                ]);
                return $this->responseService->error('缺少上传ID参数', 400);
            }

            // 如果没有提供category_id，使用默认值
            if (!$categoryId) {
                Log::warning('未提供category_id，使用默认值', [
                    'upload_id' => $uploadId,
                    'video_type' => $videoType
                ]);
                // 根据视频类型选择默认分类
                $categoryId = $videoType === 'short' ? 1 : 5; // 短视频默认搞笑，长视频默认电影
            }

            // 检查合并结果，如果没有则尝试自动合并
            $mergeResult = $this->getMergeResultFromUploadId($uploadId);

            if (!$mergeResult) {
                Log::warning('未找到缓存的合并结果，尝试自动合并', ['upload_id' => $uploadId]);

                try {
                    // 尝试自动合并分片
                    $mergeResult = $this->uploadService->mergeChunks($uploadId);

                    if ($mergeResult) {
                        // 将合并结果存储到缓存中
                        Cache::set('merge_result_' . $uploadId, $mergeResult, 3600);
                        Log::info('自动合并成功', [
                            'upload_id' => $uploadId,
                            'merge_result' => $mergeResult
                        ]);
                    }
                } catch (\Exception $e) {
                    Log::error('自动合并失败', [
                        'upload_id' => $uploadId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            if (!$mergeResult) {
                Log::error('无法获取或生成合并结果', ['upload_id' => $uploadId]);
                return $this->responseService->error('文件合并信息不存在，请重新上传', 400);
            }

            Log::info('获取到合并结果', [
                'upload_id' => $uploadId,
                'merge_result' => $mergeResult
            ]);

            // 生成缩略图
            $coverImage = '';
            try {
                $absolutePath = root_path() . 'public' . $mergeResult['path'];
                Log::info('准备生成缩略图', [
                    'absolute_path' => $absolutePath,
                    'file_exists' => file_exists($absolutePath),
                    'file_size' => file_exists($absolutePath) ? filesize($absolutePath) : 0
                ]);

                $processDir = root_path() . 'runtime' . DIRECTORY_SEPARATOR . 'video_processing' . DIRECTORY_SEPARATOR . 'temp_' . time();
                $coverImage = $this->videoProcessingService->generateThumbnail($absolutePath, $processDir);

                Log::info('缩略图生成成功', ['cover_image' => $coverImage]);
            } catch (\Exception $e) {
                Log::error('分片上传缩略图生成失败', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'absolute_path' => $absolutePath ?? 'unknown'
                ]);
            }

            // 创建视频记录
            $videoData = [
                'title' => $title ?: $this->generateTitleFromFilename($mergeResult['filename']),
                'description' => $request->param('description', ''),
                'source_type' => 'upload', // 标记为上传视频
                'video_type' => $videoType,
                'category_id' => $categoryId,
                'user_id' => $request->userInfo['id'],
                'file_path' => $mergeResult['path'],
                'cover_image' => $coverImage,
                'duration' => 0,
                'file_size' => $mergeResult['size'],
                'width' => 0,
                'height' => 0,
                'format' => pathinfo($mergeResult['filename'], PATHINFO_EXTENSION),
                'view_count' => 0,
                'like_count' => 0,
                'comment_count' => 0,
                'share_count' => 0,
                'collect_count' => 0,
                'completion_rate' => 0.00,
                'engagement_score' => 0.00,
                'is_featured' => 0,
                'is_private' => 0,
                'is_free' => 1,
                'points_price' => 0,
                'is_vip' => 0,
                'status' => 'published', // 设为已发布状态
                'audit_status' => 'pending'
            ];

            Log::info('准备创建视频记录', [
                'video_data' => $videoData,
                'upload_id' => $uploadId
            ]);

            try {
                $videoId = Db::table('videos')->insertGetId($videoData);
                Log::info('视频记录创建成功', [
                    'video_id' => $videoId,
                    'upload_id' => $uploadId
                ]);
            } catch (\Exception $e) {
                Log::error('创建视频记录失败', [
                    'upload_id' => $uploadId,
                    'video_data' => $videoData,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                throw new \Exception('创建视频记录失败：' . $e->getMessage());
            }

            // 创建视频处理状态记录
            try {
                Log::info('开始创建视频处理状态记录', ['video_id' => $videoId]);

                // 1. 上传完成状态
                $uploadStatusId = $this->videoProcessingStatusService->createProcessingStatus(
                    $videoId,
                    VideoProcessingStatusService::TYPE_UPLOAD,
                    [
                        'status' => VideoProcessingStatusService::STATUS_COMPLETED,
                        'progress' => 100,
                        'message' => '文件上传完成',
                        'completed_at' => date('Y-m-d H:i:s')
                    ]
                );

                Log::info('上传状态记录创建成功', ['status_id' => $uploadStatusId]);

                // 2. 缩略图生成状态
                $thumbnailStatus = $coverImage ? VideoProcessingStatusService::STATUS_COMPLETED : VideoProcessingStatusService::STATUS_FAILED;
                $thumbnailStatusId = $this->videoProcessingStatusService->createProcessingStatus(
                    $videoId,
                    VideoProcessingStatusService::TYPE_THUMBNAIL,
                    [
                        'status' => $thumbnailStatus,
                        'progress' => $coverImage ? 100 : 0,
                        'message' => $coverImage ? '缩略图生成成功' : '缩略图生成失败',
                        'completed_at' => date('Y-m-d H:i:s')
                    ]
                );

                Log::info('缩略图状态记录创建成功', ['status_id' => $thumbnailStatusId]);

                // 3. 转码处理状态（待处理）
                $this->videoProcessingStatusService->createProcessingStatus(
                    $videoId,
                    VideoProcessingStatusService::TYPE_TRANSCODE,
                    [
                        'status' => VideoProcessingStatusService::STATUS_PENDING,
                        'progress' => 0,
                        'message' => '等待转码处理'
                    ]
                );

                // 4. 审核状态（待审核）
                $this->videoProcessingStatusService->createProcessingStatus(
                    $videoId,
                    VideoProcessingStatusService::TYPE_AUDIT,
                    [
                        'status' => VideoProcessingStatusService::STATUS_PENDING,
                        'progress' => 0,
                        'message' => '等待管理员审核'
                    ]
                );

                Log::info('视频处理状态记录创建完成', ['video_id' => $videoId]);
            } catch (\Exception $e) {
                Log::error('创建视频处理状态记录失败', [
                    'video_id' => $videoId,
                    'error' => $e->getMessage()
                ]);
            }

            // 启动视频转码处理
            $this->triggerVideoProcessing($videoId, $mergeResult['path']);

            return $this->responseService->success([
                'video_id' => $videoId,
                'upload_id' => $uploadId,
                'file_path' => $mergeResult['path'],
                'file_size' => $mergeResult['size'],
                'status' => 'completed'
            ], '分片上传完成');

        } catch (\Exception $e) {
            Log::error('分片上传完成失败', [
                'user_id' => $request->userInfo['id'] ?? null,
                'upload_id' => $request->param('upload_id'),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return $this->responseService->error('上传完成失败：' . $e->getMessage(), 500);
        }
    }

    /**
     * 触发视频处理
     */
    private function triggerVideoProcessing(int $videoId, string $filePath): void
    {
        try {
            // 检查文件是否存在
            $fullPath = root_path() . 'public' . $filePath;
            if (!file_exists($fullPath)) {
                Log::warning('视频文件不存在，跳过处理', ['video_id' => $videoId, 'file_path' => $filePath]);
                return;
            }

            // 创建处理状态记录
            Db::table('video_processing_status')->where('video_id', $videoId)->delete();
            Db::table('video_processing_status')->insert([
                'video_id' => $videoId,
                'status' => 'pending',
                'progress' => 0,
                'current_step' => '等待处理',
                'job_id' => 'upload_' . time() . '_' . $videoId,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            // 启动后台处理进程
            $command = "php " . root_path() . "packages/api/process_single_video.php {$videoId}";

            if (PHP_OS_FAMILY === 'Windows') {
                pclose(popen("start /B {$command}", "r"));
            } else {
                exec("{$command} > /dev/null 2>&1 &");
            }

            Log::channel('video')->info('启动视频处理任务', ['video_id' => $videoId]);

        } catch (\Exception $e) {
            Log::error('启动视频处理失败', [
                'video_id' => $videoId,
                'file_path' => $filePath,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 从uploadId获取合并结果
     */
    private function getMergeResultFromUploadId(string $uploadId): ?array
    {
        // 从缓存中获取合并结果
        $mergeResult = Cache::get('merge_result_' . $uploadId);

        if ($mergeResult) {
            Log::info('从缓存获取合并结果成功', [
                'upload_id' => $uploadId,
                'cache_key' => 'merge_result_' . $uploadId
            ]);
            return $mergeResult;
        }

        Log::warning('未找到缓存的合并结果', [
            'upload_id' => $uploadId,
            'cache_key' => 'merge_result_' . $uploadId
        ]);

        return null;
    }
}
