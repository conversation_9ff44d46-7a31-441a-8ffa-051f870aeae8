<?php
declare(strict_types=1);

namespace app\controller\admin;

use app\controller\BaseController;
use think\facade\Log;
use think\facade\Db;
use think\facade\Cache;
use think\Response;

/**
 * 管理员视频处理控制器
 * 
 * 提供FFmpeg进程控制、队列管理等功能
 */
class VideoProcessing extends BaseController
{
    /**
     * 获取视频处理状态
     */
    public function status(): Response
    {
        try {
            // 检查FFmpeg是否可用
            $ffmpegAvailable = $this->checkFFmpegAvailable();
            
            // 检查队列工作进程状态
            $queueWorkerStatus = $this->checkQueueWorkerStatus();
            
            // 获取处理队列统计
            $queueStats = $this->getQueueStats();
            
            // 获取系统配置
            $config = $this->getProcessingConfig();
            
            return $this->responseService->success([
                'ffmpeg_available' => $ffmpegAvailable,
                'queue_worker_status' => $queueWorkerStatus,
                'queue_stats' => $queueStats,
                'config' => $config
            ], '获取状态成功');
            
        } catch (\Exception $e) {
            Log::error('获取视频处理状态失败', ['error' => $e->getMessage()]);
            return $this->responseService->error('获取状态失败');
        }
    }
    
    /**
     * 启动队列工作进程
     */
    public function startWorker(): Response
    {
        try {
            // 检查是否已经在运行
            if ($this->isWorkerRunning()) {
                return $this->responseService->error('队列工作进程已在运行中');
            }
            
            // 启动队列工作进程
            $result = $this->startQueueWorker();
            
            if ($result['success']) {
                Log::info('管理员启动队列工作进程', [
                    'admin_id' => $this->request->userInfo['id'] ?? 0,
                    'pid' => $result['pid'] ?? null
                ]);
                
                return $this->responseService->success([
                    'pid' => $result['pid'] ?? null,
                    'message' => '队列工作进程启动成功'
                ]);
            } else {
                return $this->responseService->error('启动失败: ' . $result['message']);
            }
            
        } catch (\Exception $e) {
            Log::error('启动队列工作进程失败', ['error' => $e->getMessage()]);
            return $this->responseService->error('启动失败');
        }
    }
    
    /**
     * 停止队列工作进程
     */
    public function stopWorker(): Response
    {
        try {
            $result = $this->stopQueueWorker();
            
            if ($result['success']) {
                Log::info('管理员停止队列工作进程', [
                    'admin_id' => $this->request->userInfo['id'] ?? 0
                ]);
                
                return $this->responseService->success([
                    'message' => '队列工作进程已停止'
                ]);
            } else {
                return $this->responseService->error('停止失败: ' . $result['message']);
            }
            
        } catch (\Exception $e) {
            Log::error('停止队列工作进程失败', ['error' => $e->getMessage()]);
            return $this->responseService->error('停止失败');
        }
    }
    
    /**
     * 重启队列工作进程
     */
    public function restartWorker(): Response
    {
        try {
            // 先停止
            $this->stopQueueWorker();
            
            // 等待一下
            sleep(2);
            
            // 再启动
            $result = $this->startQueueWorker();
            
            if ($result['success']) {
                Log::info('管理员重启队列工作进程', [
                    'admin_id' => $this->request->userInfo['id'] ?? 0,
                    'pid' => $result['pid'] ?? null
                ]);
                
                return $this->responseService->success([
                    'pid' => $result['pid'] ?? null,
                    'message' => '队列工作进程重启成功'
                ]);
            } else {
                return $this->responseService->error('重启失败: ' . $result['message']);
            }
            
        } catch (\Exception $e) {
            Log::error('重启队列工作进程失败', ['error' => $e->getMessage()]);
            return $this->responseService->error('重启失败');
        }
    }
    
    /**
     * 批量重新处理视频
     */
    public function batchReprocess(): Response
    {
        try {
            $videoIds = $this->request->param('video_ids', []);
            
            if (empty($videoIds) || !is_array($videoIds)) {
                return $this->responseService->error('请选择要重新处理的视频');
            }
            
            $successCount = 0;
            $failedCount = 0;
            $errors = [];
            
            foreach ($videoIds as $videoId) {
                try {
                    $video = Db::table('videos')->where('id', $videoId)->find();
                    
                    if (!$video) {
                        $failedCount++;
                        $errors[] = "视频ID {$videoId}: 视频不存在";
                        continue;
                    }
                    
                    if (empty($video['file_path'])) {
                        $failedCount++;
                        $errors[] = "视频ID {$videoId}: 文件路径为空";
                        continue;
                    }
                    
                    // 添加到处理队列
                    $this->addVideoToQueue($videoId, $video['file_path']);
                    $successCount++;
                    
                } catch (\Exception $e) {
                    $failedCount++;
                    $errors[] = "视频ID {$videoId}: " . $e->getMessage();
                }
            }
            
            Log::info('管理员批量重新处理视频', [
                'admin_id' => $this->request->userInfo['id'] ?? 0,
                'success_count' => $successCount,
                'failed_count' => $failedCount
            ]);
            
            return $this->responseService->success([
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'errors' => $errors
            ], "成功提交 {$successCount} 个视频重新处理");
            
        } catch (\Exception $e) {
            Log::error('批量重新处理视频失败', ['error' => $e->getMessage()]);
            return $this->responseService->error('批量处理失败');
        }
    }
    
    /**
     * 清理处理队列
     */
    public function clearQueue(): Response
    {
        try {
            // 清理失败的任务
            $failedCount = Db::table('video_processing_status')
                ->where('status', 'failed')
                ->where('created_at', '<', date('Y-m-d H:i:s', strtotime('-1 day')))
                ->delete();
            
            // 清理超时的任务
            $timeoutCount = Db::table('video_processing_status')
                ->where('status', 'processing')
                ->where('created_at', '<', date('Y-m-d H:i:s', strtotime('-2 hours')))
                ->update(['status' => 'failed', 'message' => '处理超时']);
            
            Log::info('管理员清理处理队列', [
                'admin_id' => $this->request->userInfo['id'] ?? 0,
                'failed_count' => $failedCount,
                'timeout_count' => $timeoutCount
            ]);
            
            return $this->responseService->success([
                'failed_count' => $failedCount,
                'timeout_count' => $timeoutCount
            ], '队列清理完成');
            
        } catch (\Exception $e) {
            Log::error('清理处理队列失败', ['error' => $e->getMessage()]);
            return $this->responseService->error('清理失败');
        }
    }
    
    /**
     * 检查FFmpeg是否可用
     */
    private function checkFFmpegAvailable(): bool
    {
        try {
            exec('which ffmpeg 2>/dev/null', $output, $returnCode);
            return $returnCode === 0 && !empty($output);
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 检查队列工作进程状态
     */
    private function checkQueueWorkerStatus(): array
    {
        $pidFile = runtime_path() . 'queue_worker.pid';
        
        if (!file_exists($pidFile)) {
            return ['running' => false, 'pid' => null, 'message' => 'PID文件不存在'];
        }
        
        $pid = (int)file_get_contents($pidFile);
        
        if ($pid <= 0) {
            return ['running' => false, 'pid' => null, 'message' => 'PID无效'];
        }
        
        // 检查进程是否存在
        if (PHP_OS_FAMILY === 'Windows') {
            exec("tasklist /FI \"PID eq {$pid}\" 2>nul", $output);
            $running = count($output) > 2;
        } else {
            exec("ps -p {$pid} 2>/dev/null", $output);
            $running = count($output) > 1;
        }
        
        return [
            'running' => $running,
            'pid' => $pid,
            'message' => $running ? '运行中' : '进程不存在'
        ];
    }
    
    /**
     * 获取队列统计信息
     */
    private function getQueueStats(): array
    {
        return [
            'pending' => Db::table('video_processing_status')->where('status', 'pending')->count(),
            'processing' => Db::table('video_processing_status')->where('status', 'processing')->count(),
            'completed' => Db::table('video_processing_status')->where('status', 'completed')->count(),
            'failed' => Db::table('video_processing_status')->where('status', 'failed')->count()
        ];
    }
    
    /**
     * 获取处理配置
     */
    private function getProcessingConfig(): array
    {
        return [
            'auto_process' => Cache::get('video_auto_process', true),
            'max_concurrent' => Cache::get('video_max_concurrent', 3),
            'quality_levels' => Cache::get('video_quality_levels', ['720p', '480p', '360p'])
        ];
    }
    
    /**
     * 检查工作进程是否运行
     */
    private function isWorkerRunning(): bool
    {
        $status = $this->checkQueueWorkerStatus();
        return $status['running'];
    }
    
    /**
     * 启动队列工作进程
     */
    private function startQueueWorker(): array
    {
        try {
            $scriptPath = root_path() . 'packages/api/start_queue_worker.php';
            $pidFile = runtime_path() . 'queue_worker.pid';
            $logFile = runtime_path() . 'log/queue_worker.log';
            
            if (PHP_OS_FAMILY === 'Windows') {
                $command = "php \"{$scriptPath}\" > \"{$logFile}\" 2>&1";
                $proc = proc_open("start /B {$command}", [], $pipes);
                if (is_resource($proc)) {
                    proc_close($proc);
                    sleep(1);
                    
                    if (file_exists($pidFile)) {
                        $pid = (int)file_get_contents($pidFile);
                        return ['success' => true, 'pid' => $pid];
                    }
                }
            } else {
                $command = "nohup php \"{$scriptPath}\" > \"{$logFile}\" 2>&1 & echo $!";
                $pid = (int)shell_exec($command);
                
                if ($pid > 0) {
                    file_put_contents($pidFile, $pid);
                    return ['success' => true, 'pid' => $pid];
                }
            }
            
            return ['success' => false, 'message' => '启动命令执行失败'];
            
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * 停止队列工作进程
     */
    private function stopQueueWorker(): array
    {
        try {
            $pidFile = runtime_path() . 'queue_worker.pid';
            
            if (!file_exists($pidFile)) {
                return ['success' => true, 'message' => '进程未运行'];
            }
            
            $pid = (int)file_get_contents($pidFile);
            
            if ($pid <= 0) {
                unlink($pidFile);
                return ['success' => true, 'message' => 'PID无效，已清理'];
            }
            
            // 终止进程
            if (PHP_OS_FAMILY === 'Windows') {
                exec("taskkill /F /PID {$pid} 2>nul");
            } else {
                exec("kill -TERM {$pid} 2>/dev/null");
            }
            
            // 清理PID文件
            unlink($pidFile);
            
            return ['success' => true, 'message' => '进程已停止'];
            
        } catch (\Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * 添加视频到处理队列
     */
    private function addVideoToQueue(int $videoId, string $filePath): void
    {
        // 创建处理状态记录
        Db::table('video_processing_status')->where('video_id', $videoId)->delete();
        Db::table('video_processing_status')->insert([
            'video_id' => $videoId,
            'process_type' => 'transcode',
            'status' => 'pending',
            'progress' => 0,
            'message' => '等待处理',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }
}
