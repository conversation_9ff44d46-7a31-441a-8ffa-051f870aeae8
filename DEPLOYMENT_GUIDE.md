# 🚀 视频平台部署指南

> **版本**: v2.0.0  
> **更新时间**: 2025-07-26  
> **适用环境**: 开发环境、测试环境、生产环境

---

## 📋 目录

- [系统要求](#系统要求)
- [快速开始](#快速开始)
- [开发环境部署](#开发环境部署)
- [生产环境部署](#生产环境部署)
- [Docker部署](#docker部署)
- [环境配置](#环境配置)
- [数据库配置](#数据库配置)
- [性能优化](#性能优化)
- [监控配置](#监控配置)
- [故障排除](#故障排除)

---

## 💻 系统要求

### 最低配置

| 组件 | 要求 |
|------|------|
| **操作系统** | Linux (Ubuntu 20.04+) / Windows 10+ / macOS 10.15+ |
| **CPU** | 2核心 |
| **内存** | 4GB RAM |
| **存储** | 20GB 可用空间 |
| **网络** | 100Mbps |

### 推荐配置

| 组件 | 要求 |
|------|------|
| **操作系统** | Linux (Ubuntu 22.04 LTS) |
| **CPU** | 4核心+ |
| **内存** | 8GB+ RAM |
| **存储** | 100GB+ SSD |
| **网络** | 1Gbps |

### 软件依赖

| 软件 | 版本要求 |
|------|----------|
| **Docker** | 20.10+ |
| **Docker Compose** | 2.0+ |
| **PHP** | 8.2+ |
| **MySQL** | 8.0+ |
| **Redis** | 7.0+ |
| **Nginx** | 1.20+ |
| **Node.js** | 18.0+ |
| **FFmpeg** | 4.4+ (视频处理) |

---

## ⚡ 快速开始

### 1. 克隆项目

```bash
git clone <项目地址>
cd zhengshiban
```

### 2. 一键启动 (Docker)

```bash
# Windows用户
start.bat

# Linux/macOS用户
chmod +x start.sh
./start.sh
```

### 3. 访问应用

- **用户前端**: http://localhost:3002
- **管理后台**: http://localhost:3001
- **API接口**: http://localhost:3000

### 4. 默认账号

| 类型 | 用户名 | 密码 |
|------|--------|------|
| 管理员 | admin | admin123 |
| 测试用户 | testuser | password123 |

---

## 🛠️ 开发环境部署

### 手动部署步骤

#### 1. 环境准备

```bash
# 安装PHP 8.2
sudo apt update
sudo apt install php8.2 php8.2-fpm php8.2-mysql php8.2-redis php8.2-gd php8.2-curl php8.2-zip php8.2-xml

# 安装Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer

# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装MySQL
sudo apt install mysql-server-8.0

# 安装Redis
sudo apt install redis-server

# 安装Nginx
sudo apt install nginx

# 安装FFmpeg
sudo apt install ffmpeg

# 或者使用我们提供的优化脚本预下载FFmpeg二进制文件
# 这样可以避免每次Docker构建时重新下载FFmpeg
bash scripts/download-ffmpeg.sh
```

#### 2. 配置数据库

```bash
# 登录MySQL
sudo mysql -u root -p

# 创建数据库和用户
CREATE DATABASE zhengshiban_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'zhengshiban'@'localhost' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON zhengshiban_dev.* TO 'zhengshiban'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# 导入数据库结构
mysql -u zhengshiban -p zhengshiban_dev < database_setup/complete_database_structure.sql
```

#### 3. 配置后端

```bash
cd packages/api

# 安装依赖
composer install

# 复制环境配置
cp .env.example .env

# 编辑配置文件
vim .env
```

#### 4. 配置前端

```bash
# 配置管理后台
cd packages/admin
npm install
npm run build

# 配置用户前端
cd ../frontend
npm install
npm run build
```

#### 5. 配置Nginx

```bash
# 创建配置文件
sudo vim /etc/nginx/sites-available/zhengshiban

# 启用站点
sudo ln -s /etc/nginx/sites-available/zhengshiban /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

---

## 🏭 生产环境部署

### 1. 服务器准备

#### 安全配置

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 配置防火墙
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443

# 创建应用用户
sudo adduser zhengshiban
sudo usermod -aG sudo zhengshiban
```

#### SSL证书配置

```bash
# 安装Certbot
sudo apt install certbot python3-certbot-nginx

# 获取SSL证书
sudo certbot --nginx -d your-domain.com

# 设置自动续期
sudo crontab -e
# 添加: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 2. 生产环境配置

#### 环境变量配置

```bash
# 生产环境配置
export APP_ENV=production
export APP_DEBUG=false
export DOMAIN=your-domain.com

# 数据库配置
export MYSQL_ROOT_PASSWORD=your-secure-root-password
export MYSQL_PASSWORD=your-secure-password

# 安全密钥
export JWT_SECRET=your-jwt-secret-key
export API_KEY_ADMIN=your-admin-api-key
export ENCRYPTION_KEY=your-encryption-key
```

#### 性能优化配置

```bash
# PHP-FPM优化
sudo vim /etc/php/8.2/fpm/pool.d/www.conf

# 关键配置项
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500

# MySQL优化
sudo vim /etc/mysql/mysql.conf.d/mysqld.cnf

# 关键配置项
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
max_connections = 200
query_cache_size = 64M

# Redis优化
sudo vim /etc/redis/redis.conf

# 关键配置项
maxmemory 1gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
```

### 3. 监控配置

#### 系统监控

```bash
# 安装监控工具
sudo apt install htop iotop nethogs

# 配置日志轮转
sudo vim /etc/logrotate.d/zhengshiban

/var/log/zhengshiban/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload nginx
    endscript
}
```

#### 应用监控

```bash
# 安装Supervisor
sudo apt install supervisor

# 配置进程监控
sudo vim /etc/supervisor/conf.d/zhengshiban.conf

[program:zhengshiban-worker]
command=php think queue:work
directory=/var/www/zhengshiban/packages/api
autostart=true
autorestart=true
user=www-data
numprocs=4
redirect_stderr=true
stdout_logfile=/var/log/zhengshiban/worker.log
```

---

## 🐳 Docker部署

### 开发环境

```bash
# 启动开发环境
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 生产环境

```bash
# 使用生产配置
docker-compose -f docker-compose.production.yml up -d

# 扩展服务
docker-compose -f docker-compose.production.yml up -d --scale api=3

# 更新服务
docker-compose -f docker-compose.production.yml pull
docker-compose -f docker-compose.production.yml up -d
```

### Docker配置优化

#### FFmpeg构建优化

为了避免每次构建时重新下载FFmpeg，我们提供了优化的构建方案：

```bash
# 方案1: 使用优化构建脚本（推荐）
bash scripts/build-optimized.sh --use-optimized

# 方案2: 手动下载FFmpeg后构建
bash scripts/download-ffmpeg.sh
docker-compose build api

# 方案3: 强制重新下载FFmpeg并优化构建
bash scripts/build-optimized.sh --download-ffmpeg --use-optimized --no-cache
```

**优化效果对比：**
- 标准构建：每次需要下载FFmpeg（~100MB），耗时5-10分钟
- 优化构建：首次下载后缓存，后续构建耗时1-2分钟
- 服务器部署：建议使用优化构建，显著减少部署时间

#### 生产环境Docker Compose

```yaml
# docker-compose.production.yml
version: '3.8'

services:
  api:
    build:
      context: ./packages/api
      dockerfile: Dockerfile.production
    deploy:
      replicas: 3
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
    volumes:
      - uploads:/var/www/html/uploads
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/production.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - uploads:/var/www/html/uploads
    depends_on:
      - api
    networks:
      - app-network

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: zhengshiban_prod
      MYSQL_USER: zhengshiban
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/production.cnf:/etc/mysql/conf.d/custom.cnf
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - app-network

  redis:
    image: redis:7-alpine
    command: redis-server --appendonly yes --maxmemory 1gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - app-network

volumes:
  mysql_data:
  redis_data:
  uploads:

networks:
  app-network:
    driver: bridge
```

---

## ⚙️ 环境配置

### 后端环境配置 (.env)

```bash
# 应用配置
APP_ENV=production
APP_DEBUG=false
APP_NAME="视频平台"
APP_URL=https://your-domain.com

# 数据库配置
DATABASE_TYPE=mysql
DATABASE_HOSTNAME=mysql
DATABASE_DATABASE=zhengshiban_prod
DATABASE_USERNAME=zhengshiban
DATABASE_PASSWORD=your_secure_password
DATABASE_HOSTPORT=3306
DATABASE_CHARSET=utf8mb4

# Redis配置
REDIS_HOSTNAME=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_SELECT=0

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRE=3600
JWT_REFRESH_EXPIRE=604800

# 文件上传配置
UPLOAD_MAX_SIZE=100M
UPLOAD_ALLOWED_TYPES=mp4,avi,mov,wmv,flv,mkv,webm
UPLOAD_PATH=/var/www/html/uploads

# 邮件配置
MAIL_DRIVER=smtp
MAIL_HOST=smtp.example.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_mail_password
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="视频平台"

# 第三方服务配置
QINIU_ACCESS_KEY=your_qiniu_access_key
QINIU_SECRET_KEY=your_qiniu_secret_key
QINIU_BUCKET=your_bucket_name
QINIU_DOMAIN=your_cdn_domain

# 监控配置
SENTRY_DSN=your_sentry_dsn
LOG_LEVEL=error
```

### 前端环境配置

#### 管理后台 (.env.production)

```bash
# API配置
VITE_API_BASE_URL=https://api.your-domain.com
VITE_APP_TITLE=视频平台管理后台
VITE_APP_VERSION=2.0.0

# 功能开关
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=false

# CDN配置
VITE_CDN_URL=https://cdn.your-domain.com
```

#### 用户前端 (.env.production)

```bash
# API配置
VITE_API_BASE_URL=https://api.your-domain.com
VITE_APP_TITLE=视频平台
VITE_APP_VERSION=2.0.0

# 第三方服务
VITE_ANALYTICS_ID=your_analytics_id
VITE_SENTRY_DSN=your_sentry_dsn
```

---

## 🗄️ 数据库配置

### MySQL配置优化

#### 生产环境配置 (my.cnf)

```ini
[mysqld]
# 基础配置
port = 3306
socket = /var/run/mysqld/mysqld.sock
pid-file = /var/run/mysqld/mysqld.pid
datadir = /var/lib/mysql
tmpdir = /tmp

# 字符集配置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci
init_connect = 'SET NAMES utf8mb4'

# 连接配置
max_connections = 200
max_connect_errors = 10000
max_allowed_packet = 64M
interactive_timeout = 600
wait_timeout = 600

# InnoDB配置
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2
innodb_file_per_table = 1
innodb_open_files = 400

# 查询缓存
query_cache_type = 1
query_cache_size = 64M
query_cache_limit = 2M

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 二进制日志
log-bin = mysql-bin
binlog_format = ROW
expire_logs_days = 7
```

### 数据库备份策略

#### 自动备份脚本

```bash
#!/bin/bash
# backup_database.sh

# 配置
DB_NAME="zhengshiban_prod"
DB_USER="zhengshiban"
DB_PASS="your_password"
BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
mysqldump -u$DB_USER -p$DB_PASS \
  --single-transaction \
  --routines \
  --triggers \
  $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/backup_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "backup_*.sql.gz" -mtime +7 -delete

echo "数据库备份完成: backup_$DATE.sql.gz"
```

#### 设置定时备份

```bash
# 添加到crontab
crontab -e

# 每天凌晨2点备份
0 2 * * * /path/to/backup_database.sh >> /var/log/backup.log 2>&1
```

---

## 🚀 性能优化

### 应用层优化

#### PHP优化

```ini
# php.ini 优化配置
memory_limit = 256M
max_execution_time = 300
max_input_time = 300
post_max_size = 100M
upload_max_filesize = 100M
max_file_uploads = 20

# OPcache配置
opcache.enable = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 4000
opcache.revalidate_freq = 60
opcache.fast_shutdown = 1
```

#### Nginx优化

```nginx
# nginx.conf 优化配置
worker_processes auto;
worker_connections 1024;

# Gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

# 缓存配置
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# 限流配置
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req zone=api burst=20 nodelay;
```

### 数据库优化

#### 索引优化

```sql
-- 视频表索引优化
ALTER TABLE videos ADD INDEX idx_status_audit (status, audit_status);
ALTER TABLE videos ADD INDEX idx_category_type (category_id, video_type);
ALTER TABLE videos ADD INDEX idx_user_status (user_id, status);
ALTER TABLE videos ADD INDEX idx_created_views (created_at, view_count);

-- 用户表索引优化
ALTER TABLE users ADD INDEX idx_status_created (status, created_at);
ALTER TABLE users ADD INDEX idx_email_status (email, status);

-- 评论表索引优化
ALTER TABLE video_comments ADD INDEX idx_video_status (video_id, status);
ALTER TABLE video_comments ADD INDEX idx_created_at (created_at);
```

### 缓存优化

#### Redis配置

```bash
# redis.conf 优化配置
maxmemory 2gb
maxmemory-policy allkeys-lru
timeout 300
tcp-keepalive 60

# 持久化配置
save 900 1
save 300 10
save 60 10000
```

#### 应用缓存策略

```php
// 缓存配置
return [
    'video_list' => 300,        // 视频列表缓存5分钟
    'video_detail' => 600,      // 视频详情缓存10分钟
    'category_list' => 1800,    // 分类列表缓存30分钟
    'user_stats' => 900,        // 用户统计缓存15分钟
    'hot_videos' => 600,        // 热门视频缓存10分钟
    'system_config' => 3600,    // 系统配置缓存1小时
];
```

---

## 📊 监控配置

### 系统监控

#### Prometheus + Grafana

```yaml
# docker-compose.monitoring.yml
version: '3.8'

services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards

  node-exporter:
    image: prom/node-exporter
    ports:
      - "9100:9100"

volumes:
  prometheus_data:
  grafana_data:
```

### 应用监控

#### 日志配置

```php
// 日志配置
return [
    'default' => 'file',
    'channels' => [
        'file' => [
            'type' => 'file',
            'path' => '/var/log/zhengshiban/app.log',
            'level' => 'info',
            'max_files' => 30,
        ],
        'error' => [
            'type' => 'file',
            'path' => '/var/log/zhengshiban/error.log',
            'level' => 'error',
        ],
        'performance' => [
            'type' => 'file',
            'path' => '/var/log/zhengshiban/performance.log',
            'level' => 'info',
        ]
    ]
];
```

#### 健康检查

```bash
#!/bin/bash
# health_check.sh

# 检查服务状态
check_service() {
    local service=$1
    local url=$2
    
    response=$(curl -s -o /dev/null -w "%{http_code}" $url)
    if [ $response -eq 200 ]; then
        echo "✅ $service is healthy"
        return 0
    else
        echo "❌ $service is unhealthy (HTTP $response)"
        return 1
    fi
}

# 检查各个服务
check_service "API" "http://localhost:3000/api/health"
check_service "Admin" "http://localhost:3001"
check_service "Frontend" "http://localhost:3002"

# 检查数据库连接
mysql -u zhengshiban -p$MYSQL_PASSWORD -e "SELECT 1" > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ MySQL is healthy"
else
    echo "❌ MySQL is unhealthy"
fi

# 检查Redis连接
redis-cli ping > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Redis is healthy"
else
    echo "❌ Redis is unhealthy"
fi
```

---

## 🔧 故障排除

### 常见问题

#### 1. 数据库连接失败

```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 检查端口是否开放
netstat -tlnp | grep 3306

# 检查用户权限
mysql -u root -p
SHOW GRANTS FOR 'zhengshiban'@'localhost';
```

#### 2. Redis连接失败

```bash
# 检查Redis服务状态
sudo systemctl status redis

# 检查Redis配置
redis-cli config get "*"

# 测试连接
redis-cli ping
```

#### 3. 文件上传失败

```bash
# 检查目录权限
ls -la /var/www/html/uploads/

# 修复权限
sudo chown -R www-data:www-data /var/www/html/uploads/
sudo chmod -R 755 /var/www/html/uploads/

# 检查磁盘空间
df -h
```

#### 4. 性能问题

```bash
# 检查系统资源
htop
iotop
nethogs

# 检查慢查询
mysql -u root -p -e "SHOW PROCESSLIST;"

# 检查PHP-FPM状态
sudo systemctl status php8.2-fpm
```

### 日志分析

#### 错误日志位置

```bash
# 应用日志
/var/log/zhengshiban/app.log
/var/log/zhengshiban/error.log

# 系统日志
/var/log/nginx/error.log
/var/log/mysql/error.log
/var/log/redis/redis-server.log

# PHP日志
/var/log/php8.2-fpm.log
```

#### 日志分析命令

```bash
# 查看最新错误
tail -f /var/log/zhengshiban/error.log

# 统计错误类型
grep "ERROR" /var/log/zhengshiban/app.log | awk '{print $4}' | sort | uniq -c

# 查找慢查询
grep "slow" /var/log/mysql/slow.log

# 分析访问日志
awk '{print $1}' /var/log/nginx/access.log | sort | uniq -c | sort -nr | head -10
```

---

## 📞 技术支持

### 联系方式

- **技术支持**: <EMAIL>
- **紧急联系**: +86-xxx-xxxx-xxxx
- **在线文档**: https://docs.example.com
- **问题反馈**: https://github.com/example/issues

### 支持时间

- **工作日**: 9:00 - 18:00 (UTC+8)
- **紧急支持**: 24/7 (生产环境问题)

---

*本部署指南持续更新中，如有疑问请联系技术支持团队。*
