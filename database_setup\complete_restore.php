<?php
/**
 * 完整的数据库恢复脚本
 * 根据迁移文件创建所有需要的表
 */

$host = 'mysql';
$username = 'root';
$password = 'root123456';
$database = 'zhengshiban_dev';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]);
    
    echo "数据库连接成功\n";
    
    // 禁用外键检查
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    
    // 1. 创建用户表（已存在，但确保结构正确）
    echo "创建/更新用户表...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `users` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID',
            `username` varchar(50) NOT NULL COMMENT '用户名',
            `email` varchar(100) NOT NULL COMMENT '邮箱',
            `password` varchar(255) NOT NULL COMMENT '密码',
            `nickname` varchar(100) DEFAULT NULL COMMENT '昵称',
            `avatar` varchar(500) DEFAULT NULL COMMENT '头像URL',
            `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
            `gender` enum('male','female','other') DEFAULT 'other' COMMENT '性别',
            `birthday` date DEFAULT NULL COMMENT '生日',
            `bio` text COMMENT '个人简介',
            `status` enum('active','inactive','banned') DEFAULT 'active' COMMENT '用户状态',
            `last_login_time` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `idx_username` (`username`),
            UNIQUE KEY `idx_email` (`email`),
            KEY `idx_status` (`status`),
            KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表'
    ");
    
    // 2. 创建管理员表
    echo "创建/更新管理员表...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `admins` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
            `username` varchar(50) NOT NULL COMMENT '用户名',
            `email` varchar(100) NOT NULL COMMENT '邮箱',
            `password` varchar(255) NOT NULL COMMENT '密码',
            `name` varchar(50) DEFAULT NULL COMMENT '姓名',
            `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
            `role_id` int DEFAULT 1 COMMENT '角色ID',
            `permissions` json DEFAULT NULL COMMENT '权限',
            `status` varchar(20) DEFAULT 'active' COMMENT '状态',
            `last_login_time` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
            `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `username` (`username`),
            KEY `idx_status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='管理员表'
    ");
    
    // 3. 创建分类表
    echo "创建/更新分类表...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `categories` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '分类ID',
            `name` varchar(100) NOT NULL COMMENT '分类名称',
            `slug` varchar(100) DEFAULT NULL COMMENT '分类别名',
            `description` text COMMENT '分类描述',
            `parent_id` bigint unsigned DEFAULT 0 COMMENT '父分类ID',
            `level` int DEFAULT 1 COMMENT '分类层级',
            `sort_order` int DEFAULT 0 COMMENT '排序',
            `icon` varchar(255) DEFAULT NULL COMMENT '图标',
            `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图片',
            `is_featured` tinyint(1) DEFAULT 0 COMMENT '是否推荐',
            `status` enum('active','inactive') DEFAULT 'active' COMMENT '状态',
            `seo_title` varchar(200) DEFAULT NULL COMMENT 'SEO标题',
            `seo_description` varchar(500) DEFAULT NULL COMMENT 'SEO描述',
            `seo_keywords` varchar(500) DEFAULT NULL COMMENT 'SEO关键词',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `idx_slug` (`slug`),
            KEY `idx_parent_id` (`parent_id`),
            KEY `idx_status` (`status`),
            KEY `idx_sort_order` (`sort_order`),
            KEY `idx_is_featured` (`is_featured`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类表'
    ");
    
    // 4. 创建视频分类表（兼容现有数据）
    echo "创建/更新视频分类表...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `video_categories` (
            `id` int unsigned NOT NULL AUTO_INCREMENT,
            `name` varchar(50) NOT NULL COMMENT '分类名称',
            `description` text COMMENT '分类描述',
            `video_type` enum('short','long','live') NOT NULL DEFAULT 'short' COMMENT '视频类型',
            `parent_id` int unsigned DEFAULT 0 COMMENT '父分类ID',
            `sort_order` int DEFAULT 0 COMMENT '排序',
            `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `video_type` (`video_type`),
            KEY `status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频分类表'
    ");
    
    // 5. 更新视频表结构（兼容现有数据，添加缺失字段）
    echo "创建/更新视频表...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `videos` (
            `id` int unsigned NOT NULL AUTO_INCREMENT,
            `title` varchar(255) NOT NULL COMMENT '视频标题',
            `description` text COMMENT '视频描述',
            `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图片',
            `file_path` varchar(255) DEFAULT NULL COMMENT '视频文件路径',
            `hls_url` varchar(255) DEFAULT NULL COMMENT 'HLS播放地址',
            `duration` int DEFAULT 0 COMMENT '视频时长（秒）',
            `file_size` bigint DEFAULT 0 COMMENT '文件大小（字节）',
            `video_type` enum('short','long','live') NOT NULL DEFAULT 'short' COMMENT '视频类型',
            `category_id` int unsigned DEFAULT NULL COMMENT '分类ID',
            `user_id` int unsigned NOT NULL COMMENT '用户ID',
            `view_count` int DEFAULT 0 COMMENT '观看次数',
            `like_count` int DEFAULT 0 COMMENT '点赞数',
            `comment_count` int DEFAULT 0 COMMENT '评论数',
            `share_count` int DEFAULT 0 COMMENT '分享数',
            `is_vip` tinyint(1) DEFAULT 0 COMMENT '是否VIP视频',
            `status` enum('draft','published','private','deleted') DEFAULT 'published' COMMENT '状态',
            `audit_status` enum('pending','approved','rejected') DEFAULT 'pending' COMMENT '审核状态',
            `audit_message` text COMMENT '审核信息',
            `tags` json DEFAULT NULL COMMENT '标签',
            `source_url` varchar(500) DEFAULT NULL COMMENT '原始来源URL',
            `collected_at` timestamp NULL DEFAULT NULL COMMENT '采集时间',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `user_id` (`user_id`),
            KEY `category_id` (`category_id`),
            KEY `video_type` (`video_type`),
            KEY `status` (`status`),
            KEY `audit_status` (`audit_status`),
            KEY `created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频表'
    ");

    // 6. 创建视频点赞表
    echo "创建视频点赞表...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `video_likes` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '点赞ID',
            `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
            `video_id` bigint unsigned NOT NULL COMMENT '视频ID',
            `type` enum('like','dislike') DEFAULT 'like' COMMENT '类型：like点赞，dislike踩',
            `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
            `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `uk_user_video` (`user_id`,`video_id`),
            KEY `idx_video_id` (`video_id`),
            KEY `idx_user_id` (`user_id`),
            KEY `idx_type` (`type`),
            KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频点赞表'
    ");

    echo "✅ 基础表创建完成\n";

} catch (Exception $e) {
    echo "❌ 创建失败: " . $e->getMessage() . "\n";
    exit(1);
}
?>
