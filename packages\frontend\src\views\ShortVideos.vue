<template>
  <ErrorBoundary>
    <div class="short-videos-page">
      <!-- 侧边栏遮罩 -->
      <div v-if="showMenu" class="sidebar-overlay" @click="showMenu = false"></div>

      <!-- 侧边栏 -->
      <div class="sidebar" :class="{ active: showMenu }">
        <div class="sidebar-header">
          <h3>短视频分类</h3>
          <div class="close-btn" @click="showMenu = false">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
              <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
          </div>
        </div>
        <div class="sidebar-content">
          <div class="category-section">
            <h4>视频分类</h4>
            <div class="category-list">
              <div
                v-for="tab in navigationTabs"
                :key="tab.key"
                class="category-item"
                :class="{ active: activeTab === tab.key }"
                @click="selectCategory(tab.key)"
              >
                <div class="category-icon"></div>
                <div class="category-name">{{ tab.label }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 顶部导航 -->
      <TopNavigation
        :tabs="navigationTabs"
        :activeTab="activeTab"
        @switchTab="switchTab"
        @toggleMenu="showMenu = !showMenu"
        @toggleSearch="showSearch = !showSearch"
      />

      <!-- 加载状态 -->
      <div v-if="loading && shortVideos.length === 0" class="loading-container">
        <div class="loading-spinner"></div>
        <p class="loading-text">正在加载精彩短视频...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error && shortVideos.length === 0" class="error-container">
        <div class="error-icon">😕</div>
        <p class="error-message">{{ error }}</p>
        <button @click="loadShortVideos(true)" class="retry-button">重试</button>
      </div>

      <!-- 短视频容器 - 类似抖音快手的全屏滑动 -->
      <div
        v-else-if="shortVideos.length > 0"
        class="video-container"
        @wheel="handleWheel"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
      >
      <div 
        class="video-list"
        :style="{ transform: `translateY(-${currentIndex * 100}vh)` }"
      >
        <div
          v-for="(video, index) in shortVideos"
          :key="video.id"
          class="video-item"
          :class="{ active: index === currentIndex }"
        >
          <!-- 视频播放区域 -->
          <div class="video-player">
            <!-- Video.js播放器容器 -->
            <div
              :ref="el => playerRefs[index] = el"
              class="videojs-container"
              @click="togglePlay(index)"
            >
              <video
                :ref="el => videoRefs[index] = el"
                class="video-js vjs-default-skin"
                preload="auto"
                data-setup="{}"
              ></video>
            </div>

            <!-- 播放/暂停按钮 -->
            <div
              v-if="!video.isPlaying"
              class="play-button"
              @click="togglePlay(index)"
            >
              <svg viewBox="0 0 24 24" fill="white">
                <path d="M8 5v14l11-7z"/>
              </svg>
            </div>
          </div>

          <!-- 右侧操作栏 -->
          <div class="video-actions">
            <!-- 用户头像 -->
            <div class="user-avatar" @click="goToProfile(video.author.id)">
              <img :src="video.author.avatar" :alt="video.author.name">
              <div v-if="!video.author.isFollowing" class="follow-btn">+</div>
            </div>

            <!-- 点赞 -->
            <div class="action-item" @click="toggleLike(video)">
              <svg viewBox="0 0 24 24" :fill="video.isLiked ? '#ff2d55' : 'white'">
                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
              </svg>
              <span>{{ formatCount(video.likes) }}</span>
            </div>

            <!-- 评论 -->
            <div class="action-item" @click="showComments(video)">
              <svg viewBox="0 0 24 24" fill="white">
                <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
              </svg>
              <span>{{ formatCount(video.comments) }}</span>
            </div>

            <!-- 分享 -->
            <div class="action-item" @click="shareVideo(video)">
              <svg viewBox="0 0 24 24" fill="white">
                <path d="M18 16.08c-.76 0-1.44.3-1.96.77L8.91 12.7c.05-.23.09-.46.09-.7s-.04-.47-.09-.7l7.05-4.11c.54.5 1.25.81 2.04.81 1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3c0 .***********.7L8.04 9.81C7.5 9.31 6.79 9 6 9c-1.66 0-3 1.34-3 3s1.34 3 3 3c.79 0 1.5-.31 2.04-.81l7.12 4.16c-.05.21-.08.43-.08.65 0 1.61 1.31 2.92 2.92 2.92s2.92-1.31 2.92-2.92-1.31-2.92-2.92-2.92z"/>
              </svg>
              <span>分享</span>
            </div>
          </div>

          <!-- 底部信息栏 -->
          <div class="video-info">
            <div class="user-info">
              <span class="username">@{{ video.author.name }}</span>
              <span class="description">{{ video.description }}</span>
            </div>
            <div class="music-info" v-if="video.music">
              <svg viewBox="0 0 24 24" fill="white">
                <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
              </svg>
              <span>{{ video.music.name }} - {{ video.music.artist }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索框 -->
    <div v-if="showSearch" class="search-overlay">
      <div class="search-container">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索短视频..."
          class="search-input"
          @keyup.enter="performSearch"
        />
        <button class="search-btn" @click="performSearch">搜索</button>
        <button class="close-search" @click="showSearch = false">✕</button>
      </div>
    </div>
    </div>
  </ErrorBoundary>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import TopNavigation from '@/components/TopNavigation.vue'
import ErrorBoundary from '@/components/ErrorBoundary.vue'
import { videoApi } from '@/api/video'
import Hls from 'hls.js'

// 导航标签配置 - 动态加载短视频分类
const navigationTabs = ref([
  { key: 'recommend', label: '推荐' },
  { key: 'latest', label: '最新' }
])

// 加载短视频分类
const loadShortVideoCategories = async () => {
  try {
    console.log('🔄 开始加载短视频分类...')
    const response = await videoApi.getCategories()
    console.log('📡 短视频分类API响应:', response)

    if (response.success === true) {
      console.log('📋 所有分类数据:', response.data)
      // 过滤出短视频分类
      const shortVideoCategories = response.data.filter((cat: any) => {
        console.log(`🔍 检查分类: ${cat.name}, video_type: ${cat.video_type}`)
        return cat.video_type === 'short'
      })

      // 添加分类到导航标签 - 保留推荐、最新，然后添加短视频分类
      const categoryTabs = shortVideoCategories.map((cat: any) => ({
        key: `category_${cat.id}`,
        label: cat.name
      }))

      navigationTabs.value = [
        { key: 'recommend', label: '推荐' },
        { key: 'latest', label: '最新' },
        ...categoryTabs
      ]

      // 更新侧边栏分类 - 显示所有分类
      categories.value = shortVideoCategories

      console.log('✅ 短视频分类加载成功:', shortVideoCategories)
      console.log('🏷️ 导航标签已更新:', navigationTabs.value)
    } else {
      console.error('❌ 短视频分类API返回错误:', response)
    }
  } catch (error) {
    console.error('❌ 加载短视频分类失败:', error)
  }
}

const activeTab = ref('recommend')
const showMenu = ref(false)
const showSearch = ref(false)
const categories = ref([])
const searchQuery = ref('')
const currentIndex = ref(0)
const playerRefs = ref([])
const videoRefs = ref([])
const vjsPlayers = ref<HTMLVideoElement[]>([]) // 存储HTML5视频元素
const hlsInstances = ref<(Hls | null)[]>([]) // 存储HLS实例
const playingLocks = ref<boolean[]>([]) // 播放状态锁，防止快速点击冲突

// 短视频数据 - 集成真实API
const shortVideos = ref<any[]>([])
const loading = ref(false)
const error = ref('')
const currentPage = ref(1)
const hasMore = ref(true)

// 预加载策略配置
const preloadStrategy = {
  enabled: true,
  maxConcurrent: 2,
  preloadDistance: 1 // 只预加载相邻1个视频
}

// 加载短视频数据
const loadShortVideos = async (refresh = false) => {
  if (loading.value) return

  try {
    loading.value = true
    error.value = ''

    // 构建请求参数
    const params: any = {
      video_type: 'short',
      page: refresh ? 1 : currentPage.value,
      limit: 10,
      sort: 'created_at',
      order: 'desc'
    }

    // 根据不同的标签设置不同的排序
    if (activeTab.value === 'recommend') {
      params.sort = 'view_count'
      params.order = 'desc'
    } else if (activeTab.value === 'latest') {
      params.sort = 'created_at'
      params.order = 'desc'
    }

    // 如果选择了特定分类，添加分类过滤
    if (activeTab.value.startsWith('category_')) {
      const categoryId = activeTab.value.replace('category_', '')
      params.category_id = parseInt(categoryId)
    }

    const response = await videoApi.getVideoList(params)

    if (response.code === 200 && response.data) {
      const newVideos = response.data.data || []

      // 转换数据格式以适配现有组件
      const formattedVideos = newVideos.map((video: any) => ({
        id: video.id,
        videoUrl: video.hls_path || video.file_path || '',
        coverUrl: video.cover_image || '',
        description: video.description || video.title || '',
        author: {
          id: video.user_id || 1,
          name: video.nickname || video.username || '用户',
          avatar: video.avatar || 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=50&h=50&fit=crop&crop=face',
          isFollowing: false
        },
        likes: video.like_count || 0,
        comments: video.comment_count || 0,
        shares: video.share_count || 0,
        isLiked: false,
        isPlaying: false,
        music: {
          name: '背景音乐',
          artist: '未知'
        }
      }))

      if (refresh) {
        shortVideos.value = formattedVideos
        currentPage.value = 1
        // 初始化播放锁数组
        playingLocks.value = new Array(formattedVideos.length).fill(false)
      } else {
        shortVideos.value = [...shortVideos.value, ...formattedVideos]
        // 扩展播放锁数组
        playingLocks.value = [...playingLocks.value, ...new Array(formattedVideos.length).fill(false)]
      }

      hasMore.value = response.data.has_more || false
      if (!refresh) {
        currentPage.value++
      }

      // 预加载视频
      if (preloadStrategy.enabled) {
        preloadVideos(formattedVideos)
      }
    } else {
      throw new Error(response.message || '加载失败')
    }
  } catch (err) {
    console.error('加载短视频失败:', err)
    error.value = err instanceof Error ? err.message : '加载失败'

    // 强制显示错误，不使用备用数据，确保我们能看到真实的API问题
    console.error('API调用详细错误:', {
      error: err,
      message: err instanceof Error ? err.message : '未知错误',
      stack: err instanceof Error ? err.stack : undefined
    })

    // 暂时注释掉备用数据，强制解决API问题
    // if (shortVideos.value.length === 0) {
    //   shortVideos.value = getFallbackVideos()
    //   playingLocks.value = new Array(shortVideos.value.length).fill(false)
    // }
  } finally {
    loading.value = false
  }
}

// 预加载视频
const preloadVideos = (videos: any[]) => {
  videos.slice(0, preloadStrategy.maxConcurrent).forEach(video => {
    if (video.videoUrl) {
      const videoElement = document.createElement('video')
      videoElement.preload = 'metadata'
      videoElement.src = video.videoUrl
      videoElement.muted = true
    }
  })
}

// 获取备用数据（当API失败时使用）
const getFallbackVideos = () => [
  {
    id: 1,
    videoUrl: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
    coverUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=600&fit=crop',
    description: '大自然的美丽风光，让人心旷神怡的旅行体验 🌄 #风景 #旅行 #自然',
    author: {
      id: 1,
      name: '旅行达人小王',
      avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=50&h=50&fit=crop&crop=face',
      isFollowing: false
    },
    likes: 12500,
    comments: 890,
    shares: 234,
    isLiked: false,
    isPlaying: false,
    music: {
      name: '夏日清风',
      artist: '轻音乐'
    }
  }
]

// 触摸相关变量
const touchStartY = ref(0)
const touchEndY = ref(0)

const switchTab = async (tab: string) => {
  activeTab.value = tab
  // 切换标签时重新加载数据
  await loadShortVideos(true)
}

const selectCategory = async (categoryKey: string) => {
  activeTab.value = categoryKey
  showMenu.value = false
  // 切换分类时重新加载数据
  await loadShortVideos(true)
}

const formatCount = (count: number) => {
  if (count >= 10000) {
    return (count / 10000).toFixed(1) + 'w'
  }
  return count.toString()
}

const togglePlay = async (index: number) => {
  console.log('🎬 点击播放按钮，视频索引:', index)

  // 检查播放锁，防止快速点击冲突
  if (playingLocks.value[index]) {
    console.log('🔒 播放操作进行中，忽略重复点击')
    return
  }

  const videoElement = videoRefs.value[index] as HTMLVideoElement
  const videoData = shortVideos.value[index]

  if (!videoElement || !videoData) {
    console.error('❌ 视频元素或数据不存在:', { videoElement: !!videoElement, videoData: !!videoData })
    return
  }

  // 设置播放锁
  playingLocks.value[index] = true

  try {
    if (videoElement.paused) {
      console.log('▶️ 开始播放视频')
      // 先暂停其他视频，避免冲突
      pauseOtherVideos(index)

      // 等待一小段时间确保其他视频已暂停
      await new Promise(resolve => setTimeout(resolve, 50))

      await videoElement.play()
      videoData.isPlaying = true
    } else {
      console.log('⏸️ 暂停视频')
      videoElement.pause()
      videoData.isPlaying = false
    }
  } catch (error) {
    console.error('❌ 播放/暂停失败:', error)

    // 重置播放状态
    videoData.isPlaying = false

    // 根据错误类型提供不同的处理
    if (error.name === 'NotAllowedError') {
      console.log('ℹ️ 浏览器阻止了自动播放，这是正常的安全策略')
      // 显示播放按钮，让用户手动点击
      videoData.isPlaying = false
    } else if (error.name === 'AbortError') {
      console.log('ℹ️ 播放操作被中断（正常情况）')
    } else {
      // 其他错误尝试重新初始化播放器
      console.log('🔄 尝试重新初始化播放器')
      initVideoPlayer(index, videoData)
    }
  } finally {
    // 释放播放锁
    setTimeout(() => {
      playingLocks.value[index] = false
    }, 300) // 增加锁定时间，防止过快点击
  }
}

// 获取视频类型
const getVideoType = (src: string): string => {
  if (src.includes('.m3u8')) return 'application/x-mpegURL'
  if (src.includes('.mpd')) return 'application/dash+xml'
  if (src.includes('.mp4')) return 'video/mp4'
  if (src.includes('.webm')) return 'video/webm'
  return 'video/mp4'
}

// 初始化HTML5视频播放器
const initVideoPlayer = (index: number, video: any) => {
  const videoElement = videoRefs.value[index]
  if (!videoElement || !video.videoUrl) return

  try {
    // 清理现有的HLS实例
    if (hlsInstances.value[index]) {
      hlsInstances.value[index]?.destroy()
      hlsInstances.value[index] = null
    }

    // 设置视频属性
    videoElement.poster = video.coverUrl || ''
    videoElement.muted = true
    videoElement.loop = true
    videoElement.playsInline = true
    videoElement.preload = 'auto'
    videoElement.controls = false // 短视频模式隐藏控制栏

    const videoUrl = video.videoUrl

    // 检查是否为HLS流
    if (videoUrl.includes('.m3u8')) {
      if (Hls.isSupported()) {
        // 使用HLS.js播放HLS流
        const hls = new Hls({
          enableWorker: true,
          lowLatencyMode: true,
          backBufferLength: 90
        })

        hls.loadSource(videoUrl)
        hls.attachMedia(videoElement)

        hls.on(Hls.Events.MANIFEST_PARSED, () => {
          console.log(`短视频 ${index} HLS manifest parsed`)
          video.isLoaded = true
        })

        hls.on(Hls.Events.ERROR, (event, data) => {
          console.error(`短视频 ${index} HLS错误:`, data)
          if (data.fatal) {
            video.hasError = true
          }
        })

        hlsInstances.value[index] = hls
      } else if (videoElement.canPlayType('application/vnd.apple.mpegurl')) {
        // Safari原生支持HLS
        videoElement.src = videoUrl
      } else {
        console.error(`短视频 ${index} 浏览器不支持HLS`)
        video.hasError = true
        return
      }
    } else {
      // 普通视频文件
      videoElement.src = videoUrl
    }

    // 事件监听
    videoElement.addEventListener('loadeddata', () => {
      console.log(`短视频 ${index} 加载完成`)
      video.isLoaded = true
    })

    videoElement.addEventListener('error', (error) => {
      console.error(`短视频 ${index} 播放错误:`, error)
      video.hasError = true
    })

    videoElement.addEventListener('play', () => {
      video.isPlaying = true
      // 暂停其他视频
      pauseOtherVideos(index)
    })

    videoElement.addEventListener('pause', () => {
      video.isPlaying = false
    })

    videoElement.addEventListener('ended', () => {
      video.isPlaying = false
      // 自动播放下一个视频
      if (index < shortVideos.value.length - 1) {
        nextVideo()
      }
    })

    // 存储video元素引用
    vjsPlayers.value[index] = videoElement
  } catch (err) {
    console.error(`初始化短视频播放器 ${index} 失败:`, err)
    video.hasError = true
  }
}

// 暂停其他视频
const pauseOtherVideos = (currentIndex: number) => {
  videoRefs.value.forEach((videoElement, index) => {
    if (videoElement && index !== currentIndex && !videoElement.paused) {
      videoElement.pause()
      shortVideos.value[index].isPlaying = false
    }
  })
}

const toggleLike = (video: any) => {
  video.isLiked = !video.isLiked
  if (video.isLiked) {
    video.likes++
  } else {
    video.likes--
  }
}

const goToProfile = (userId: number) => {
  console.log('Go to profile:', userId)
}

const showComments = (video: any) => {
  console.log('Show comments for video:', video.id)
}

const shareVideo = (video: any) => {
  console.log('Share video:', video.id)
}

const performSearch = () => {
  console.log('Search:', searchQuery.value)
  showSearch.value = false
}

// 滑动相关函数
const handleWheel = (event: WheelEvent) => {
  event.preventDefault()
  if (event.deltaY > 0 && currentIndex.value < shortVideos.value.length - 1) {
    nextVideo()
  } else if (event.deltaY < 0 && currentIndex.value > 0) {
    prevVideo()
  }
}

const handleTouchStart = (event: TouchEvent) => {
  touchStartY.value = event.touches[0].clientY
}

const handleTouchMove = (event: TouchEvent) => {
  event.preventDefault()
}

const handleTouchEnd = (event: TouchEvent) => {
  touchEndY.value = event.changedTouches[0].clientY
  const diff = touchStartY.value - touchEndY.value
  
  if (Math.abs(diff) > 50) {
    if (diff > 0 && currentIndex.value < shortVideos.value.length - 1) {
      nextVideo()
    } else if (diff < 0 && currentIndex.value > 0) {
      prevVideo()
    }
  }
}

const nextVideo = () => {
  if (currentIndex.value < shortVideos.value.length - 1) {
    pauseCurrentVideo()
    currentIndex.value++
    nextTick(async () => {
      await playCurrentVideo()
      // 预加载下一个视频
      await preloadVideo(currentIndex.value + 1)
    })
  }
}

const prevVideo = () => {
  if (currentIndex.value > 0) {
    pauseCurrentVideo()
    currentIndex.value--
    nextTick(() => {
      playCurrentVideo()
    })
  }
}

const pauseCurrentVideo = () => {
  const currentVideoElement = videoRefs.value[currentIndex.value] as HTMLVideoElement
  if (currentVideoElement) {
    currentVideoElement.pause()
    shortVideos.value[currentIndex.value].isPlaying = false
  }
}

const playCurrentVideo = async () => {
  const index = currentIndex.value
  const currentVideoElement = videoRefs.value[index] as HTMLVideoElement
  const video = shortVideos.value[index]

  if (!currentVideoElement || !video) {
    console.log('⚠️ 当前视频元素或数据不存在')
    return
  }

  // 检查播放锁
  if (playingLocks.value[index]) {
    console.log('🔒 当前视频播放操作进行中')
    return
  }

  // 设置播放锁
  playingLocks.value[index] = true

  try {
    // 先暂停其他视频
    pauseOtherVideos(index)

    // 等待一小段时间确保其他视频已暂停
    await new Promise(resolve => setTimeout(resolve, 50))

    // 检查是否需要用户交互才能播放
    if (currentVideoElement.paused) {
      // 尝试播放，但不强制自动播放
      console.log('ℹ️ 视频需要用户点击才能播放（浏览器自动播放策略）')
      video.isPlaying = false
    }
  } catch (error) {
    console.error('❌ 当前视频播放失败:', error)
    video.isPlaying = false

    // 如果是NotAllowedError，说明需要用户交互
    if (error.name === 'NotAllowedError') {
      console.log('ℹ️ 需要用户点击播放按钮（浏览器自动播放策略）')
    } else if (error.name === 'AbortError') {
      console.log('ℹ️ 当前视频播放操作被中断（正常情况）')
    } else {
      // 其他错误尝试重新初始化播放器
      console.log('🔄 尝试重新初始化当前视频播放器')
      initVideoPlayer(index, video)
    }
  } finally {
    // 释放播放锁
    setTimeout(() => {
      playingLocks.value[index] = false
    }, 300) // 增加锁定时间，防止过快点击
  }
}

// 预加载视频
const preloadVideo = async (index: number) => {
  if (index >= 0 && index < shortVideos.value.length && !vjsPlayers.value[index]) {
    const video = shortVideos.value[index]
    if (video && !video.isLoaded) {
      initVideoPlayer(index, video)
    }
  }
}

onMounted(async () => {
  // 加载短视频分类
  await loadShortVideoCategories()

  // 加载短视频数据
  await loadShortVideos(true)

  // 初始化第一个视频播放器（不自动播放）
  nextTick(() => {
    if (shortVideos.value.length > 0) {
      initVideoPlayer(0, shortVideos.value[0])
      console.log('ℹ️ 视频播放器已初始化，点击播放按钮开始播放')
    }
  })
})

onUnmounted(() => {
  // 清理所有HLS实例
  hlsInstances.value.forEach((hls) => {
    if (hls) {
      hls.destroy()
    }
  })
  hlsInstances.value = []

  // 清理所有视频元素
  vjsPlayers.value.forEach((player: HTMLVideoElement) => {
    if (player) {
      player.pause()
      player.src = ''
    }
  })
  vjsPlayers.value = []
})
</script>

<style scoped>
.short-videos-page {
  position: relative;
  width: 100vw;
  height: 100vh;
  background: #000000;
  overflow: hidden;
}

/* 加载状态样式 */
.loading-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  color: white;
  z-index: 100;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid #1989fa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 1.1rem;
  color: #cccccc;
  text-align: center;
  margin: 0;
}

/* 错误状态样式 */
.error-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  color: white;
  text-align: center;
  padding: 40px 20px;
  z-index: 100;
}

.error-icon {
  font-size: 4rem;
  margin-bottom: 10px;
}

.error-message {
  font-size: 1.1rem;
  color: #cccccc;
  margin: 0 0 20px 0;
  line-height: 1.5;
}

.retry-button {
  background: linear-gradient(45deg, #1989fa, #0d7ce8);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(25, 137, 250, 0.3);
}

.retry-button:hover {
  background: linear-gradient(45deg, #0d7ce8, #0050b3);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(25, 137, 250, 0.4);
}

.video-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.video-list {
  position: relative;
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease-out;
}

.video-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-player {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000000;
}

.videojs-container {
  width: 100%;
  height: 100%;
}

/* Video.js样式覆盖 - 短视频模式 */
.video-player :deep(.video-js) {
  width: 100% !important;
  height: 100% !important;
  background: transparent;
}

.video-player :deep(.vjs-tech) {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}

/* 隐藏Video.js的控制栏（短视频模式） */
.video-player :deep(.vjs-control-bar) {
  display: none !important;
}

.video-player :deep(.vjs-big-play-button) {
  display: none !important;
}

.video-player :deep(.vjs-loading-spinner) {
  display: none !important;
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.play-button:hover {
  background: rgba(0, 0, 0, 0.7);
  transform: translate(-50%, -50%) scale(1.1);
}

.play-button svg {
  width: 32px;
  height: 32px;
  margin-left: 4px;
}

.video-actions {
  position: absolute;
  right: 20px;
  bottom: 120px;
  display: flex;
  flex-direction: column;
  gap: 25px;
  z-index: 15;
  align-items: center;
}

.user-avatar {
  position: relative;
  width: 50px;
  height: 50px;
  cursor: pointer;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 2px solid white;
  object-fit: cover;
}

.follow-btn {
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 20px;
  background: #ff2d55;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  font-weight: bold;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.action-item:hover {
  transform: scale(1.1);
}

.action-item svg {
  width: 32px;
  height: 32px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5));
}

.action-item span {
  color: white;
  font-size: 12px;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.video-info {
  position: absolute;
  left: 20px;
  bottom: 120px;
  right: 100px;
  z-index: 10;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.3));
  padding: 20px 0 0 0;
  border-radius: 8px;
}

.user-info {
  margin-bottom: 10px;
}

.username {
  color: white;
  font-size: 16px;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  display: block;
  margin-bottom: 8px;
  background: rgba(0, 0, 0, 0.3);
  padding: 4px 8px;
  border-radius: 12px;
  display: inline-block;
}

.description {
  color: white;
  font-size: 14px;
  line-height: 1.5;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.2);
  padding: 8px 12px;
  border-radius: 8px;
  backdrop-filter: blur(5px);
}

.music-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-size: 12px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.music-info svg {
  width: 16px;
  height: 16px;
}

.search-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 100px;
  z-index: 1000;
}

.search-container {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 25px;
  padding: 10px 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  max-width: 400px;
  width: 90%;
}

.search-input {
  flex: 1;
  background: transparent;
  border: none;
  color: white;
  font-size: 16px;
  outline: none;
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.search-btn,
.close-search {
  background: #1989fa;
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 15px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s ease;
}

.search-btn:hover,
.close-search:hover {
  background: #0050b3;
}

.close-search {
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .video-actions {
    right: 10px;
    bottom: 80px;
    gap: 15px;
  }

  .user-avatar {
    width: 45px;
    height: 45px;
  }

  .action-item svg {
    width: 28px;
    height: 28px;
  }

  .video-info {
    left: 10px;
    bottom: 120px; /* 向上移动，避免被按钮遮挡 */
    right: 80px; /* 增加右边距，为按钮留出空间 */
  }

  .username {
    font-size: 15px;
  }

  .description {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .video-actions {
    gap: 12px;
    bottom: 180px; /* 移动端向上移动更多 */
    right: 15px;
  }

  .user-avatar {
    width: 40px;
    height: 40px;
  }

  .action-item svg {
    width: 24px;
    height: 24px;
  }

  .action-item span {
    font-size: 11px;
  }

  .username {
    font-size: 14px;
  }

  .description {
    font-size: 12px;
  }
}

/* 侧边栏样式 */
.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1500;
  backdrop-filter: blur(2px);
}

.sidebar {
  position: fixed;
  top: 0;
  left: -300px;
  width: 280px;
  height: 100vh;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(20px);
  z-index: 1600;
  transition: left 0.3s ease;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar.active {
  left: 0;
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h3 {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.sidebar-content {
  padding: 20px;
}

.category-section h4 {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 15px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.category-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.category-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.category-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.category-item.active {
  background: rgba(25, 137, 250, 0.2);
  color: #1989fa;
  border: 1px solid rgba(25, 137, 250, 0.3);
}

.category-icon {
  width: 8px;
  height: 8px;
  background: currentColor;
  border-radius: 50%;
  flex-shrink: 0;
}

.category-name {
  font-size: 15px;
  font-weight: 500;
}
</style>
