<?php
/**
 * 创建视频播放链接表
 * 用于存储不同格式和清晰度的视频播放链接
 */

$host = 'mysql';
$username = 'root';
$password = 'root123456';
$database = 'zhengshiban_dev';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]);
    
    echo "数据库连接成功\n";
    
    // 创建视频播放链接表
    echo "创建视频播放链接表...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `video_play_urls` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '播放链接ID',
            `video_id` bigint unsigned NOT NULL COMMENT '视频ID',
            `quality` enum('240p','360p','480p','720p','1080p','1440p','2160p','original') DEFAULT '720p' COMMENT '视频清晰度',
            `format` enum('mp4','m3u8','flv','webm','dash') DEFAULT 'mp4' COMMENT '视频格式',
            `url` varchar(1000) NOT NULL COMMENT '播放链接',
            `file_size` bigint DEFAULT 0 COMMENT '文件大小（字节）',
            `bitrate` int DEFAULT 0 COMMENT '码率（kbps）',
            `duration` int DEFAULT 0 COMMENT '时长（秒）',
            `width` int DEFAULT 0 COMMENT '视频宽度',
            `height` int DEFAULT 0 COMMENT '视频高度',
            `fps` decimal(5,2) DEFAULT 0.00 COMMENT '帧率',
            `codec` varchar(50) DEFAULT NULL COMMENT '编码格式',
            `is_encrypted` tinyint(1) DEFAULT 0 COMMENT '是否加密',
            `encryption_key_id` varchar(100) DEFAULT NULL COMMENT '加密密钥ID',
            `is_primary` tinyint(1) DEFAULT 0 COMMENT '是否主要播放源',
            `cdn_url` varchar(1000) DEFAULT NULL COMMENT 'CDN加速链接',
            `status` enum('active','inactive','processing','failed') DEFAULT 'active' COMMENT '状态',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            KEY `idx_video_id` (`video_id`),
            KEY `idx_quality` (`quality`),
            KEY `idx_format` (`format`),
            KEY `idx_status` (`status`),
            KEY `idx_is_primary` (`is_primary`),
            KEY `idx_video_quality` (`video_id`,`quality`),
            KEY `idx_video_format` (`video_id`,`format`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频播放链接表'
    ");
    
    // 为现有视频添加播放链接
    echo "为现有视频添加播放链接...\n";
    
    // 获取所有视频
    $videos = $pdo->query("SELECT id, file_path, hls_url FROM videos")->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($videos as $video) {
        $videoId = $video['id'];
        $filePath = $video['file_path'];
        $hlsUrl = $video['hls_url'];
        
        // 添加原始MP4播放链接
        if ($filePath) {
            $pdo->exec("
                INSERT IGNORE INTO `video_play_urls` 
                (`video_id`, `quality`, `format`, `url`, `is_primary`, `status`) 
                VALUES 
                ($videoId, 'original', 'mp4', '$filePath', 1, 'active')
            ");
        }
        
        // 添加HLS播放链接
        if ($hlsUrl) {
            $pdo->exec("
                INSERT IGNORE INTO `video_play_urls` 
                (`video_id`, `quality`, `format`, `url`, `is_primary`, `status`) 
                VALUES 
                ($videoId, '720p', 'm3u8', '$hlsUrl', 0, 'active')
            ");
        }
        
        // 添加一些示例播放链接（不同清晰度）
        $qualities = ['240p', '360p', '480p', '720p', '1080p'];
        foreach ($qualities as $quality) {
            $exampleUrl = "/uploads/videos/video_{$videoId}_{$quality}.mp4";
            $isPrimary = ($quality === '720p') ? 1 : 0;
            
            $pdo->exec("
                INSERT IGNORE INTO `video_play_urls` 
                (`video_id`, `quality`, `format`, `url`, `is_primary`, `status`) 
                VALUES 
                ($videoId, '$quality', 'mp4', '$exampleUrl', $isPrimary, 'active')
            ");
        }
    }
    
    echo "✅ 视频播放链接表创建完成\n";
    
    // 显示统计信息
    $count = $pdo->query("SELECT COUNT(*) FROM video_play_urls")->fetchColumn();
    echo "播放链接总数: $count\n";
    
} catch (Exception $e) {
    echo "❌ 创建失败: " . $e->getMessage() . "\n";
    exit(1);
}
?>
