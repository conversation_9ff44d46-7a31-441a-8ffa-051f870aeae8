#!/bin/bash

# FFmpeg下载脚本
# 用于在构建前预下载FFmpeg二进制文件，避免每次构建时重复下载

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
FFMPEG_DIR="$PROJECT_ROOT/packages/api/ffmpeg-binaries"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 检测系统架构
detect_architecture() {
    local arch=$(uname -m)
    case $arch in
        x86_64)
            echo "amd64"
            ;;
        aarch64|arm64)
            echo "arm64"
            ;;
        *)
            print_message $RED "❌ 不支持的架构: $arch"
            exit 1
            ;;
    esac
}

# 下载FFmpeg静态构建版本
download_ffmpeg() {
    local arch=$(detect_architecture)
    local ffmpeg_version="6.1.1"
    local download_url=""
    
    print_message $BLUE "🔍 检测到系统架构: $arch"
    
    # 根据架构选择下载链接
    case $arch in
        amd64)
            download_url="https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-linux64-gpl.tar.xz"
            ;;
        arm64)
            download_url="https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-linuxarm64-gpl.tar.xz"
            ;;
    esac
    
    print_message $BLUE "📥 开始下载FFmpeg..."
    print_message $BLUE "下载地址: $download_url"
    
    # 创建临时目录
    local temp_dir=$(mktemp -d)
    local archive_file="$temp_dir/ffmpeg.tar.xz"
    
    # 下载FFmpeg
    if curl -L -o "$archive_file" "$download_url"; then
        print_message $GREEN "✅ FFmpeg下载完成"
    else
        print_message $RED "❌ FFmpeg下载失败"
        rm -rf "$temp_dir"
        exit 1
    fi
    
    # 创建目标目录
    mkdir -p "$FFMPEG_DIR"
    
    # 解压并提取二进制文件
    print_message $BLUE "📦 解压FFmpeg..."
    cd "$temp_dir"
    tar -xf "$archive_file"
    
    # 查找解压后的目录
    local extracted_dir=$(find . -maxdepth 1 -type d -name "ffmpeg-*" | head -1)
    if [ -z "$extracted_dir" ]; then
        print_message $RED "❌ 找不到解压后的FFmpeg目录"
        rm -rf "$temp_dir"
        exit 1
    fi
    
    # 复制二进制文件
    if [ -f "$extracted_dir/bin/ffmpeg" ] && [ -f "$extracted_dir/bin/ffprobe" ]; then
        cp "$extracted_dir/bin/ffmpeg" "$FFMPEG_DIR/"
        cp "$extracted_dir/bin/ffprobe" "$FFMPEG_DIR/"
        chmod +x "$FFMPEG_DIR/ffmpeg" "$FFMPEG_DIR/ffprobe"
        print_message $GREEN "✅ FFmpeg二进制文件已复制到: $FFMPEG_DIR"
    else
        print_message $RED "❌ 找不到FFmpeg二进制文件"
        rm -rf "$temp_dir"
        exit 1
    fi
    
    # 清理临时文件
    rm -rf "$temp_dir"
    
    # 验证安装
    print_message $BLUE "🔍 验证FFmpeg安装..."
    if "$FFMPEG_DIR/ffmpeg" -version > /dev/null 2>&1; then
        local version=$("$FFMPEG_DIR/ffmpeg" -version | head -1)
        print_message $GREEN "✅ FFmpeg验证成功: $version"
    else
        print_message $RED "❌ FFmpeg验证失败"
        exit 1
    fi
}

# 主函数
main() {
    print_message $BLUE "🎬 FFmpeg下载脚本"
    print_message $BLUE "================================"
    
    # 检查是否已存在FFmpeg
    if [ -f "$FFMPEG_DIR/ffmpeg" ] && [ -f "$FFMPEG_DIR/ffprobe" ]; then
        print_message $YELLOW "⚠️  FFmpeg已存在，是否重新下载? (y/N)"
        read -r response
        if [[ ! "$response" =~ ^[Yy]$ ]]; then
            print_message $BLUE "跳过下载"
            exit 0
        fi
        rm -rf "$FFMPEG_DIR"
    fi
    
    download_ffmpeg
    
    print_message $GREEN "🎉 FFmpeg下载完成！"
    print_message $BLUE "现在可以使用优化版Dockerfile进行构建："
    print_message $BLUE "docker-compose -f docker-compose.yml build api"
}

# 运行主函数
main "$@"
