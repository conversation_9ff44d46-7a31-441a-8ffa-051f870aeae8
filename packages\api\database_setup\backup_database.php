<?php
/**
 * 数据库备份脚本
 * 使用PHP方式备份数据库，确保编码正确
 */

// 数据库配置
$host = '127.0.0.1';
$port = '3306';
$username = 'root';
$password = 'shipin123456';
$database = 'zhengshiban_dev';

// 备份文件配置
$backupDir = __DIR__;
$timestamp = date('Y-m-d_H-i-s');
$backupFile = $backupDir . '/zhengshiban_clean_backup_' . $timestamp . '.sql';

try {
    // 创建PDO连接
    $dsn = "mysql:host={$host};port={$port};dbname={$database};charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]);

    echo "🔗 数据库连接成功\n";
    echo "📊 开始备份数据库: {$database}\n";

    // 开始备份
    $sql = generateBackupSQL($pdo, $database);
    
    // 写入文件
    file_put_contents($backupFile, $sql);
    
    echo "✅ 备份完成！\n";
    echo "📁 备份文件: {$backupFile}\n";
    echo "📏 文件大小: " . formatBytes(filesize($backupFile)) . "\n";

} catch (Exception $e) {
    echo "❌ 备份失败: " . $e->getMessage() . "\n";
    exit(1);
}

/**
 * 生成备份SQL
 */
function generateBackupSQL($pdo, $database) {
    $sql = '';
    
    // 添加文件头注释
    $sql .= "-- =============================================\n";
    $sql .= "-- 视频平台数据库备份文件\n";
    $sql .= "-- 生成时间: " . date('Y-m-d H:i:s') . "\n";
    $sql .= "-- 数据库名: {$database}\n";
    $sql .= "-- 字符集: utf8mb4\n";
    $sql .= "-- 排序规则: utf8mb4_unicode_ci\n";
    $sql .= "-- =============================================\n\n";
    
    $sql .= "SET NAMES utf8mb4;\n";
    $sql .= "SET FOREIGN_KEY_CHECKS = 0;\n\n";
    
    // 获取所有表
    $tables = getTables($pdo);
    
    foreach ($tables as $table) {
        echo "📋 备份表: {$table}\n";
        
        // 添加表结构
        $sql .= "-- =============================================\n";
        $sql .= "-- 表结构: {$table}\n";
        $sql .= "-- =============================================\n\n";
        
        $sql .= "DROP TABLE IF EXISTS `{$table}`;\n";
        $sql .= getTableStructure($pdo, $table) . "\n\n";
        
        // 添加表数据
        $sql .= "-- =============================================\n";
        $sql .= "-- 表数据: {$table}\n";
        $sql .= "-- =============================================\n\n";
        
        $sql .= getTableData($pdo, $table) . "\n\n";
    }
    
    $sql .= "SET FOREIGN_KEY_CHECKS = 1;\n";
    
    return $sql;
}

/**
 * 获取所有表名
 */
function getTables($pdo) {
    $stmt = $pdo->query("SHOW TABLES");
    $tables = [];
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $tables[] = $row[0];
    }
    return $tables;
}

/**
 * 获取表结构
 */
function getTableStructure($pdo, $table) {
    $stmt = $pdo->query("SHOW CREATE TABLE `{$table}`");
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    return $row['Create Table'] . ';';
}

/**
 * 获取表数据
 */
function getTableData($pdo, $table) {
    $sql = '';
    
    // 获取数据
    $stmt = $pdo->query("SELECT * FROM `{$table}`");
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($rows)) {
        return "-- 表 {$table} 无数据\n";
    }
    
    // 获取列信息
    $columns = array_keys($rows[0]);
    $columnList = '`' . implode('`, `', $columns) . '`';
    
    $sql .= "INSERT INTO `{$table}` ({$columnList}) VALUES\n";
    
    $values = [];
    foreach ($rows as $row) {
        $rowValues = [];
        foreach ($row as $value) {
            if ($value === null) {
                $rowValues[] = 'NULL';
            } else {
                // 转义特殊字符
                $escaped = addslashes($value);
                $rowValues[] = "'{$escaped}'";
            }
        }
        $values[] = '(' . implode(', ', $rowValues) . ')';
    }
    
    $sql .= implode(",\n", $values) . ";\n";
    
    return $sql;
}

/**
 * 格式化文件大小
 */
function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

echo "\n🎉 数据库备份脚本执行完成！\n";
?>
