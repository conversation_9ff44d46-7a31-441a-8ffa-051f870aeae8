<?php
/**
 * 简化的数据恢复脚本
 * 手动恢复关键数据，避免编码问题
 */

$host = 'mysql';
$username = 'root';
$password = 'root123456';
$database = 'zhengshiban_dev';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]);
    
    echo "数据库连接成功\n";
    
    // 插入管理员数据
    echo "恢复管理员数据...\n";
    $adminSql = "INSERT INTO `admins` (`id`, `username`, `email`, `password`, `name`, `role_id`, `status`, `created_at`, `updated_at`) VALUES
    (1, 'admin', '<EMAIL>', '\$2y\$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '超级管理员', 1, 'active', '2025-07-25 14:00:00', '2025-07-25 14:00:00')
    ON DUPLICATE KEY UPDATE updated_at = VALUES(updated_at)";
    
    $pdo->exec($adminSql);
    echo "✅ 管理员数据恢复成功\n";
    
    // 插入测试用户数据
    echo "恢复用户数据...\n";
    $userSql = "INSERT INTO `users` (`id`, `username`, `email`, `password`, `nickname`, `status`, `created_at`, `updated_at`) VALUES 
    (1, 'testuser', '<EMAIL>', '\$2y\$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '测试用户', 'active', '2025-07-25 14:00:00', '2025-07-25 14:00:00'),
    (2, 'user2', '<EMAIL>', '\$2y\$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '用户2', 'active', '2025-07-25 15:00:00', '2025-07-25 15:00:00')
    ON DUPLICATE KEY UPDATE updated_at = VALUES(updated_at)";
    
    $pdo->exec($userSql);
    echo "✅ 用户数据恢复成功\n";
    
    // 插入视频分类数据
    echo "恢复视频分类数据...\n";
    $categorySql = "INSERT INTO `video_categories` (`id`, `name`, `description`, `video_type`, `parent_id`, `sort_order`, `status`, `created_at`, `updated_at`) VALUES 
    (1, '搞笑', '搞笑短视频', 'short', 0, 1, 1, '2025-07-25 14:00:00', '2025-07-25 14:00:00'),
    (2, '娱乐', '娱乐短视频', 'short', 0, 2, 1, '2025-07-25 14:00:00', '2025-07-25 14:00:00'),
    (3, '生活', '生活短视频', 'short', 0, 3, 1, '2025-07-25 14:00:00', '2025-07-25 14:00:00'),
    (4, '美食', '美食短视频', 'short', 0, 4, 1, '2025-07-25 14:00:00', '2025-07-25 14:00:00'),
    (5, '电影', '电影长视频', 'long', 0, 1, 1, '2025-07-25 14:00:00', '2025-07-25 14:00:00'),
    (6, '电视剧', '电视剧长视频', 'long', 0, 2, 1, '2025-07-25 14:00:00', '2025-07-25 14:00:00'),
    (7, '纪录片', '纪录片长视频', 'long', 0, 3, 1, '2025-07-25 14:00:00', '2025-07-25 14:00:00'),
    (8, '综艺', '综艺长视频', 'long', 0, 4, 1, '2025-07-25 14:00:00', '2025-07-25 14:00:00')
    ON DUPLICATE KEY UPDATE updated_at = VALUES(updated_at)";
    
    $pdo->exec($categorySql);
    echo "✅ 视频分类数据恢复成功\n";
    
    // 插入示例视频数据
    echo "恢复示例视频数据...\n";
    $videoSql = "INSERT INTO `videos` (`id`, `title`, `description`, `cover_image`, `video_type`, `category_id`, `user_id`, `view_count`, `like_count`, `comment_count`, `status`, `audit_status`, `created_at`, `updated_at`) VALUES 
    (1, '测试短视频1', '这是一个测试短视频', '/uploads/covers/test1.jpg', 'short', 1, 1, 100, 10, 5, 'published', 'approved', '2025-07-25 14:00:00', '2025-07-25 14:00:00'),
    (2, '测试短视频2', '这是另一个测试短视频', '/uploads/covers/test2.jpg', 'short', 2, 1, 200, 20, 8, 'published', 'approved', '2025-07-25 15:00:00', '2025-07-25 15:00:00'),
    (3, '测试长视频1', '这是一个测试长视频', '/uploads/covers/test3.jpg', 'long', 5, 2, 500, 50, 15, 'published', 'approved', '2025-07-25 16:00:00', '2025-07-25 16:00:00')
    ON DUPLICATE KEY UPDATE updated_at = VALUES(updated_at)";
    
    $pdo->exec($videoSql);
    echo "✅ 示例视频数据恢复成功\n";
    
    // 验证恢复结果
    echo "\n验证恢复结果:\n";
    
    $tables = ['admins', 'users', 'video_categories', 'videos', 'video_processing_status'];
    foreach ($tables as $table) {
        $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
        echo "表 $table: $count 条记录\n";
    }
    
    // 显示管理员信息
    echo "\n管理员账户信息:\n";
    $admin = $pdo->query("SELECT username, email, name, role_id FROM admins WHERE id = 1")->fetch(PDO::FETCH_ASSOC);
    if ($admin) {
        echo "用户名: {$admin['username']}\n";
        echo "邮箱: {$admin['email']}\n";
        echo "姓名: {$admin['name']}\n";
        echo "角色ID: {$admin['role_id']}\n";
        echo "密码: password (默认)\n";
    }
    
    echo "\n🎉 数据恢复完成！\n";
    
} catch (Exception $e) {
    echo "❌ 恢复失败: " . $e->getMessage() . "\n";
}
?>
