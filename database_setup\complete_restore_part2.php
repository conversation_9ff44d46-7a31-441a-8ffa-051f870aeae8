<?php
/**
 * 完整的数据库恢复脚本 - 第二部分
 * 创建评论、收藏、系统配置等表
 */

$host = 'mysql';
$username = 'root';
$password = 'root123456';
$database = 'zhengshiban_dev';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]);
    
    echo "数据库连接成功\n";
    
    // 禁用外键检查
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    
    // 7. 创建视频评论表
    echo "创建视频评论表...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `video_comments` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '评论ID',
            `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
            `video_id` bigint unsigned NOT NULL COMMENT '视频ID',
            `parent_id` bigint unsigned DEFAULT 0 COMMENT '父评论ID',
            `reply_to_user_id` bigint unsigned DEFAULT NULL COMMENT '回复用户ID',
            `content` text NOT NULL COMMENT '评论内容',
            `content_type` enum('text','image','emoji') DEFAULT 'text' COMMENT '内容类型',
            `like_count` bigint DEFAULT 0 COMMENT '点赞数',
            `reply_count` bigint DEFAULT 0 COMMENT '回复数',
            `status` enum('pending','approved','rejected','hidden') DEFAULT 'approved' COMMENT '状态',
            `is_top` tinyint(1) DEFAULT 0 COMMENT '是否置顶',
            `is_hot` tinyint(1) DEFAULT 0 COMMENT '是否热门',
            `audit_time` datetime DEFAULT NULL COMMENT '审核时间',
            `auditor_id` bigint unsigned DEFAULT NULL COMMENT '审核员ID',
            `audit_reason` varchar(500) DEFAULT NULL COMMENT '审核原因',
            `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
            `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
            `meta_data` json DEFAULT NULL COMMENT '元数据',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            KEY `idx_video_id` (`video_id`),
            KEY `idx_user_id` (`user_id`),
            KEY `idx_parent_id` (`parent_id`),
            KEY `idx_reply_to_user_id` (`reply_to_user_id`),
            KEY `idx_status` (`status`),
            KEY `idx_is_top` (`is_top`),
            KEY `idx_is_hot` (`is_hot`),
            KEY `idx_like_count` (`like_count`),
            KEY `idx_created_at` (`created_at`),
            KEY `idx_video_status` (`video_id`,`status`),
            KEY `idx_video_parent` (`video_id`,`parent_id`),
            KEY `idx_user_status` (`user_id`,`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频评论表'
    ");
    
    // 8. 创建视频收藏表
    echo "创建视频收藏表...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `video_collections` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
            `user_id` bigint unsigned NOT NULL COMMENT '用户ID',
            `video_id` bigint unsigned NOT NULL COMMENT '视频ID',
            `collection_name` varchar(100) DEFAULT '默认收藏夹' COMMENT '收藏夹名称',
            `is_public` tinyint(1) DEFAULT 0 COMMENT '是否公开',
            `sort_order` int DEFAULT 0 COMMENT '排序',
            `notes` text COMMENT '收藏备注',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `uk_user_video` (`user_id`,`video_id`),
            KEY `idx_video_id` (`video_id`),
            KEY `idx_user_id` (`user_id`),
            KEY `idx_collection_name` (`collection_name`),
            KEY `idx_is_public` (`is_public`),
            KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频收藏表'
    ");
    
    // 9. 创建系统配置表
    echo "创建系统配置表...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `system_configs` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '配置ID',
            `config_key` varchar(100) NOT NULL COMMENT '配置键',
            `config_value` text COMMENT '配置值',
            `data_type` enum('string','int','float','bool','json','array') DEFAULT 'string' COMMENT '数据类型',
            `config_group` varchar(50) DEFAULT 'default' COMMENT '配置分组',
            `title` varchar(200) DEFAULT NULL COMMENT '配置标题',
            `description` text COMMENT '配置描述',
            `is_public` tinyint(1) DEFAULT 0 COMMENT '是否公开',
            `sort_order` int DEFAULT 0 COMMENT '排序',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `uk_config_key` (`config_key`),
            KEY `idx_config_group` (`config_group`),
            KEY `idx_is_public` (`is_public`),
            KEY `idx_sort_order` (`sort_order`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表'
    ");
    
    // 10. 创建操作日志表
    echo "创建操作日志表...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `operation_logs` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '日志ID',
            `user_id` bigint unsigned DEFAULT NULL COMMENT '用户ID',
            `admin_id` bigint unsigned DEFAULT NULL COMMENT '管理员ID',
            `action` varchar(100) NOT NULL COMMENT '操作动作',
            `resource_type` varchar(50) DEFAULT NULL COMMENT '资源类型',
            `resource_id` bigint unsigned DEFAULT NULL COMMENT '资源ID',
            `description` text COMMENT '操作描述',
            `request_data` json DEFAULT NULL COMMENT '请求数据',
            `response_data` json DEFAULT NULL COMMENT '响应数据',
            `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
            `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
            `status` enum('success','failed','error') DEFAULT 'success' COMMENT '状态',
            `error_message` text COMMENT '错误信息',
            `execution_time` decimal(8,3) DEFAULT 0.000 COMMENT '执行时间(秒)',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            PRIMARY KEY (`id`),
            KEY `idx_user_id` (`user_id`),
            KEY `idx_admin_id` (`admin_id`),
            KEY `idx_action` (`action`),
            KEY `idx_resource_type` (`resource_type`),
            KEY `idx_resource_id` (`resource_id`),
            KEY `idx_status` (`status`),
            KEY `idx_created_at` (`created_at`),
            KEY `idx_resource_type_id` (`resource_type`,`resource_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表'
    ");
    
    echo "✅ 扩展表创建完成\n";
    
    // 启用外键检查
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    
} catch (Exception $e) {
    echo "❌ 创建失败: " . $e->getMessage() . "\n";
    exit(1);
}
?>
