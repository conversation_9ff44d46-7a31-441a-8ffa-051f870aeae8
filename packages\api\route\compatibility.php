<?php
// +----------------------------------------------------------------------
// | 兼容性路由配置
// +----------------------------------------------------------------------
// | 保持向后兼容，重定向旧版本API到新版本
// +----------------------------------------------------------------------

use think\facade\Route;

// =====================================================
// 兼容性路由 - 向后兼容旧版本API
// =====================================================

// 重定向旧版本API到新版本，保持所有现有功能
Route::group('api', function () {
    
    // =====================================================
    // 用户认证路由兼容
    // =====================================================
    
    Route::group('auth', function () {
        Route::post('login', 'Auth/login');
        Route::post('register', 'Auth/register');
        Route::post('forgot-password', 'Auth/forgotPassword');
        Route::post('reset-password', 'Auth/resetPassword');
        Route::post('refresh', 'Auth/refresh');
        Route::post('logout', 'Auth/logout')->middleware(\app\middleware\UserAuth::class);
        Route::post('logout-all', 'Auth/logoutAll')->middleware(\app\middleware\UserAuth::class);
        Route::get('me', 'Auth/me')->middleware(\app\middleware\UserAuth::class);
        Route::get('profile', 'Auth/profile')->middleware(\app\middleware\UserAuth::class);
    });
    
    // =====================================================
    // 基础资源路由兼容
    // =====================================================
    
    // 视频相关接口兼容
    Route::get('videos/recommend', 'Video/recommend');
    Route::get('videos/:id', 'Video/read');
    Route::put('videos/:id', 'Video/update'); // 暂时移除认证
    Route::get('videos', 'Video/index');
    
    // 分类接口兼容
    Route::get('categories', 'Category/index');
    Route::get('categories/:id', 'Category/read');
    
    // 用户接口兼容
    Route::get('users', 'User/index');
    Route::get('users/:id', 'User/read');
    
    // =====================================================
    // 视频播放相关兼容路由
    // =====================================================
    
    // 视频处理状态接口
    Route::get('videos/processing-status/batch', 'VideoProcessingController/getBatchStatus');
    Route::get('videos/:id/processing-status', 'VideoProcessingController/getStatus');
    
    // 视频观看记录
    Route::post('videos/view/:id', 'Video/recordView');
    
    // 视频播放路由（保持原有中间件配置）
    Route::get('video/:id/play', 'Video/play')->middleware([\app\middleware\VideoAuth::class, \app\middleware\Cors::class]);
    Route::get('video/:id/stream', 'Video/stream')->middleware([\app\middleware\VideoAuth::class, \app\middleware\Cors::class]);
    Route::get('video/:id/hls', 'Video/hls')->middleware([\app\middleware\VideoAuth::class, \app\middleware\Cors::class]);
    Route::get('video/:id/info', 'Video/info')->middleware(\app\middleware\Cors::class);
    
    // =====================================================
    // 特殊用户接口兼容（保留原有复杂逻辑）
    // =====================================================
    
    // 获取用户信息 - 个人中心使用
    Route::get('user/:id/info', function($id) {
        try {
            $user = \think\facade\Db::table('users')
                ->alias('u')
                ->leftJoin('user_profiles p', 'u.id = p.user_id')
                ->field([
                    'u.id', 'u.username', 'u.email', 'u.status', 'u.created_at',
                    'p.nickname', 'p.avatar', 'p.bio', 'p.gender', 'p.birthday'
                ])
                ->where('u.id', $id)
                ->find();

            if (!$user) {
                return json([
                    'code' => 404,
                    'message' => '用户不存在',
                    'data' => null
                ]);
            }

            // 获取用户统计
            $stats = [
                'upload_count' => \think\facade\Db::table('videos')->where('user_id', $id)->count(),
                'total_views' => \think\facade\Db::table('videos')->where('user_id', $id)->sum('view_count'),
                'total_likes' => \think\facade\Db::table('videos')->where('user_id', $id)->sum('like_count'),
                'favorite_count' => \think\facade\Db::table('video_collections')->where('user_id', $id)->count()
            ];

            $user['stats'] = $stats;

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => $user
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取用户信息失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    });

    // 获取用户视频列表 - 个人中心使用
    Route::get('user/:id/videos', function($id) {
        try {
            $page = max(1, request()->get('page', 1));
            $limit = min(request()->get('limit', 20), 50);
            $offset = ($page - 1) * $limit;

            $total = \think\facade\Db::table('videos')
                ->where('user_id', $id)
                ->where('status', 'published')
                ->count();

            $videos = \think\facade\Db::table('videos')
                ->alias('v')
                ->leftJoin('categories c', 'v.category_id = c.id')
                ->field([
                    'v.id', 'v.title', 'v.description', 'v.cover_image', 'v.duration',
                    'v.view_count', 'v.like_count', 'v.is_vip', 'v.status', 'v.created_at',
                    'c.name as category_name'
                ])
                ->where('v.user_id', $id)
                ->where('v.status', 'published')
                ->order('v.created_at desc')
                ->limit($offset, $limit)
                ->select();

            return json([
                'code' => 200,
                'message' => 'success',
                'data' => [
                    'videos' => $videos->toArray(),
                    'total' => $total,
                    'page' => $page,
                    'limit' => $limit,
                    'pages' => ceil($total / $limit)
                ]
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'message' => '获取用户视频失败：' . $e->getMessage(),
                'data' => null
            ]);
        }
    });
    
    // =====================================================
    // 管理员相关兼容路由
    // =====================================================
    
    // 视频状态管理接口（需要管理员认证）
    Route::post('admin/videos/manage-status', 'Video/manageVideoStatus')->middleware(\app\middleware\AdminAuth::class);
    
    // 网络检测接口（用于上传优化）
    Route::any('ping', function() {
        return json(['code' => 200, 'message' => 'pong', 'timestamp' => time()]);
    });
    
})->middleware([\app\middleware\ApiKeyAuth::class, \app\middleware\Cors::class]);
