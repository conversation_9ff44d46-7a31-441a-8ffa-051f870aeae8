<?php
/**
 * 完整的数据库恢复脚本 - 第三部分
 * 创建视频处理、采集、队列等表
 */

$host = 'mysql';
$username = 'root';
$password = 'root123456';
$database = 'zhengshiban_dev';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]);
    
    echo "数据库连接成功\n";
    
    // 禁用外键检查
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    
    // 11. 创建/更新视频处理状态表
    echo "创建视频处理状态表...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `video_processing_status` (
            `id` int unsigned NOT NULL AUTO_INCREMENT,
            `video_id` int unsigned NOT NULL COMMENT '视频ID',
            `process_type` enum('upload','thumbnail','transcode','audit') NOT NULL COMMENT '处理类型',
            `status` enum('pending','processing','completed','failed') DEFAULT 'pending' COMMENT '处理状态',
            `progress` int DEFAULT 0 COMMENT '处理进度(0-100)',
            `current_step` varchar(255) DEFAULT NULL COMMENT '当前步骤',
            `message` text COMMENT '处理信息',
            `error_message` text COMMENT '错误信息',
            `started_at` timestamp NULL DEFAULT NULL COMMENT '开始时间',
            `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `video_id` (`video_id`),
            KEY `process_type` (`process_type`),
            KEY `status` (`status`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频处理状态表'
    ");
    
    // 12. 创建视频处理队列表
    echo "创建视频处理队列表...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `video_processing_queue` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '队列ID',
            `video_id` bigint unsigned NOT NULL COMMENT '视频ID',
            `task_type` enum('upload','thumbnail','transcode','audit','cleanup') NOT NULL COMMENT '任务类型',
            `priority` int DEFAULT 0 COMMENT '优先级',
            `status` enum('pending','processing','completed','failed','cancelled') DEFAULT 'pending' COMMENT '状态',
            `progress` int DEFAULT 0 COMMENT '进度(0-100)',
            `worker_id` varchar(100) DEFAULT NULL COMMENT '工作进程ID',
            `attempts` int DEFAULT 0 COMMENT '尝试次数',
            `max_attempts` int DEFAULT 3 COMMENT '最大尝试次数',
            `payload` json DEFAULT NULL COMMENT '任务数据',
            `result` json DEFAULT NULL COMMENT '处理结果',
            `error_message` text COMMENT '错误信息',
            `scheduled_at` timestamp NULL DEFAULT NULL COMMENT '计划执行时间',
            `started_at` timestamp NULL DEFAULT NULL COMMENT '开始时间',
            `completed_at` timestamp NULL DEFAULT NULL COMMENT '完成时间',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            KEY `idx_video_id` (`video_id`),
            KEY `idx_task_type` (`task_type`),
            KEY `idx_status` (`status`),
            KEY `idx_priority` (`priority`),
            KEY `idx_worker_id` (`worker_id`),
            KEY `idx_scheduled_at` (`scheduled_at`),
            KEY `idx_created_at` (`created_at`),
            KEY `idx_status_priority` (`status`,`priority`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频处理队列表'
    ");
    
    // 13. 创建视频加密密钥表
    echo "创建视频加密密钥表...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `video_encryption_keys` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '密钥ID',
            `video_id` bigint unsigned NOT NULL COMMENT '视频ID',
            `key_id` varchar(100) NOT NULL COMMENT '密钥标识',
            `encryption_key` varchar(255) NOT NULL COMMENT '加密密钥',
            `iv` varchar(255) DEFAULT NULL COMMENT '初始化向量',
            `algorithm` varchar(50) DEFAULT 'AES-128-CBC' COMMENT '加密算法',
            `key_uri` varchar(500) DEFAULT NULL COMMENT '密钥URI',
            `expires_at` timestamp NULL DEFAULT NULL COMMENT '过期时间',
            `is_active` tinyint(1) DEFAULT 1 COMMENT '是否激活',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `uk_video_key_id` (`video_id`,`key_id`),
            KEY `idx_video_id` (`video_id`),
            KEY `idx_key_id` (`key_id`),
            KEY `idx_is_active` (`is_active`),
            KEY `idx_expires_at` (`expires_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='视频加密密钥表'
    ");
    
    // 14. 创建采集源表
    echo "创建采集源表...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `collect_sources` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '采集源ID',
            `name` varchar(100) NOT NULL COMMENT '采集源名称',
            `api_url` varchar(500) NOT NULL COMMENT 'API地址',
            `api_type` enum('json','xml','rss') DEFAULT 'json' COMMENT 'API类型',
            `auth_key` varchar(255) DEFAULT NULL COMMENT '认证密钥',
            `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
            `description` text COMMENT '描述',
            `charset` varchar(20) DEFAULT 'utf-8' COMMENT '字符编码',
            `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
            `headers` json DEFAULT NULL COMMENT '请求头',
            `timeout` int DEFAULT 30 COMMENT '超时时间(秒)',
            `retry_times` int DEFAULT 3 COMMENT '重试次数',
            `collect_interval` int DEFAULT 3 COMMENT '采集间隔(小时)',
            `last_collect_time` timestamp NULL DEFAULT NULL COMMENT '最后采集时间',
            `total_collected` int DEFAULT 0 COMMENT '总采集数量',
            `success_rate` decimal(5,2) DEFAULT 0.00 COMMENT '成功率',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            KEY `idx_status` (`status`),
            KEY `idx_api_type` (`api_type`),
            KEY `idx_last_collect_time` (`last_collect_time`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采集源表'
    ");
    
    // 15. 创建采集任务表
    echo "创建采集任务表...\n";
    $pdo->exec("
        CREATE TABLE IF NOT EXISTS `collect_tasks` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '任务ID',
            `source_id` bigint unsigned NOT NULL COMMENT '采集源ID',
            `task_name` varchar(100) NOT NULL COMMENT '任务名称',
            `status` enum('pending','running','completed','failed','cancelled') DEFAULT 'pending' COMMENT '状态',
            `progress` int DEFAULT 0 COMMENT '进度(0-100)',
            `total_items` int DEFAULT 0 COMMENT '总项目数',
            `processed_items` int DEFAULT 0 COMMENT '已处理项目数',
            `success_items` int DEFAULT 0 COMMENT '成功项目数',
            `failed_items` int DEFAULT 0 COMMENT '失败项目数',
            `start_time` timestamp NULL DEFAULT NULL COMMENT '开始时间',
            `end_time` timestamp NULL DEFAULT NULL COMMENT '结束时间',
            `error_message` text COMMENT '错误信息',
            `log_data` json DEFAULT NULL COMMENT '日志数据',
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
            PRIMARY KEY (`id`),
            KEY `idx_source_id` (`source_id`),
            KEY `idx_status` (`status`),
            KEY `idx_start_time` (`start_time`),
            KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='采集任务表'
    ");
    
    echo "✅ 处理相关表创建完成\n";
    
    // 启用外键检查
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    
} catch (Exception $e) {
    echo "❌ 创建失败: " . $e->getMessage() . "\n";
    exit(1);
}
?>
