#!/bin/bash

# Docker构建优化脚本
# 自动下载FFmpeg并使用优化版Dockerfile构建

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 显示帮助信息
show_help() {
    echo "Docker构建优化脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --download-ffmpeg    强制重新下载FFmpeg"
    echo "  --use-optimized      使用优化版Dockerfile"
    echo "  --no-cache           不使用Docker缓存"
    echo "  --help               显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                   # 标准构建"
    echo "  $0 --use-optimized   # 使用优化版构建"
    echo "  $0 --download-ffmpeg --use-optimized  # 重新下载FFmpeg并优化构建"
}

# 检查FFmpeg是否存在
check_ffmpeg() {
    local ffmpeg_dir="$PROJECT_ROOT/packages/api/ffmpeg-binaries"
    if [ -f "$ffmpeg_dir/ffmpeg" ] && [ -f "$ffmpeg_dir/ffprobe" ]; then
        return 0
    else
        return 1
    fi
}

# 下载FFmpeg
download_ffmpeg() {
    print_message $BLUE "📥 下载FFmpeg..."
    if [ -f "$SCRIPT_DIR/download-ffmpeg.sh" ]; then
        bash "$SCRIPT_DIR/download-ffmpeg.sh"
    else
        print_message $RED "❌ 找不到FFmpeg下载脚本"
        exit 1
    fi
}

# 构建Docker镜像
build_docker() {
    local dockerfile="$1"
    local no_cache="$2"
    
    cd "$PROJECT_ROOT"
    
    local build_args=""
    if [ "$no_cache" = "true" ]; then
        build_args="--no-cache"
    fi
    
    print_message $BLUE "🐳 开始构建Docker镜像..."
    print_message $BLUE "使用Dockerfile: $dockerfile"
    
    if [ "$dockerfile" = "optimized" ]; then
        # 使用优化版Dockerfile
        docker-compose build $build_args --build-arg DOCKERFILE=Dockerfile.optimized api
    else
        # 使用标准Dockerfile
        docker-compose build $build_args api
    fi
}

# 主函数
main() {
    local download_ffmpeg_flag=false
    local use_optimized=false
    local no_cache=false
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --download-ffmpeg)
                download_ffmpeg_flag=true
                shift
                ;;
            --use-optimized)
                use_optimized=true
                shift
                ;;
            --no-cache)
                no_cache=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                print_message $RED "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    print_message $BLUE "🚀 Docker构建优化脚本"
    print_message $BLUE "================================"
    
    # 如果使用优化版，检查FFmpeg
    if [ "$use_optimized" = "true" ]; then
        if [ "$download_ffmpeg_flag" = "true" ] || ! check_ffmpeg; then
            download_ffmpeg
        else
            print_message $GREEN "✅ FFmpeg已存在，跳过下载"
        fi
        
        build_docker "optimized" "$no_cache"
    else
        print_message $YELLOW "⚠️  使用标准构建（每次都会下载FFmpeg）"
        print_message $BLUE "建议使用 --use-optimized 选项进行优化构建"
        build_docker "standard" "$no_cache"
    fi
    
    print_message $GREEN "🎉 构建完成！"
}

# 运行主函数
main "$@"
