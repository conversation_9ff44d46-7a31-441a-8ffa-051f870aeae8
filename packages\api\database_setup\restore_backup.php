<?php
/**
 * 数据库备份恢复脚本
 * 处理编码问题并恢复数据
 */

$host = 'mysql';
$username = 'root';
$password = 'root123456';
$database = 'zhengshiban_dev';

// 备份文件列表（按优先级排序）
$backupFiles = [
    'backup_method5_compatible.sql',
    'backup_method1_mysqldump_standard.sql',
    'backup_method2_mysqldump_extended.sql',
    'backup_method8_limited.sql',
    'zhengshiban_clean_backup_2025-07-26.sql'
];

function convertEncoding($content) {
    // 尝试检测编码
    $encodings = ['UTF-16LE', 'UTF-16BE', 'UTF-16', 'UTF-8', 'ISO-8859-1', 'Windows-1252'];
    
    foreach ($encodings as $encoding) {
        $converted = @iconv($encoding, 'UTF-8//IGNORE', $content);
        if ($converted !== false && strlen($converted) > 0) {
            // 检查转换后的内容是否包含SQL关键字
            if (strpos($converted, 'CREATE TABLE') !== false || 
                strpos($converted, 'INSERT INTO') !== false ||
                strpos($converted, 'MySQL dump') !== false) {
                echo "成功使用编码: $encoding\n";
                return $converted;
            }
        }
    }
    
    return false;
}

function cleanSqlContent($content) {
    // 移除BOM
    $content = preg_replace('/^\xEF\xBB\xBF/', '', $content);
    
    // 修复可能的编码问题
    $content = str_replace(["\r\n", "\r"], "\n", $content);
    
    // 移除空的字符
    $content = preg_replace('/\x00/', '', $content);
    
    return $content;
}

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]);
    
    echo "数据库连接成功\n";
    
    foreach ($backupFiles as $backupFile) {
        $filePath = '/var/www/html/database_setup/' . $backupFile;
        
        if (!file_exists($filePath)) {
            echo "文件不存在: $backupFile\n";
            continue;
        }
        
        echo "\n尝试恢复备份文件: $backupFile\n";
        echo "文件大小: " . number_format(filesize($filePath)) . " 字节\n";
        
        // 读取文件内容
        $content = file_get_contents($filePath);
        if ($content === false) {
            echo "无法读取文件: $backupFile\n";
            continue;
        }
        
        // 转换编码
        $convertedContent = convertEncoding($content);
        if ($convertedContent === false) {
            echo "编码转换失败: $backupFile\n";
            continue;
        }
        
        // 清理内容
        $cleanContent = cleanSqlContent($convertedContent);
        
        // 检查内容是否有效
        if (strlen($cleanContent) < 100) {
            echo "文件内容太短，可能无效: $backupFile\n";
            continue;
        }
        
        // 分割SQL语句
        $statements = preg_split('/;\s*\n/', $cleanContent);
        $statements = array_filter($statements, function($stmt) {
            $stmt = trim($stmt);
            return !empty($stmt) && 
                   !preg_match('/^--/', $stmt) && 
                   !preg_match('/^\/\*/', $stmt) &&
                   !preg_match('/^SET/', $stmt);
        });
        
        echo "找到 " . count($statements) . " 条SQL语句\n";
        
        if (count($statements) < 5) {
            echo "SQL语句太少，可能文件损坏: $backupFile\n";
            continue;
        }
        
        // 开始事务
        $pdo->beginTransaction();
        
        try {
            $successCount = 0;
            $errorCount = 0;
            
            // 设置SQL模式
            $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
            $pdo->exec("SET SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO'");
            
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (empty($statement)) continue;
                
                try {
                    $pdo->exec($statement);
                    $successCount++;
                    
                    // 显示进度
                    if ($successCount % 10 == 0) {
                        echo "已执行 $successCount 条语句...\n";
                    }
                } catch (PDOException $e) {
                    $errorCount++;
                    if ($errorCount < 10) { // 只显示前10个错误
                        echo "SQL错误: " . $e->getMessage() . "\n";
                        echo "语句: " . substr($statement, 0, 100) . "...\n";
                    }
                }
            }
            
            // 恢复外键检查
            $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
            
            echo "\n执行结果:\n";
            echo "成功: $successCount 条\n";
            echo "失败: $errorCount 条\n";
            
            if ($successCount > 0 && $errorCount < $successCount / 2) {
                $pdo->commit();
                echo "✅ 备份恢复成功: $backupFile\n";
                
                // 验证恢复结果
                $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
                echo "恢复的表数量: " . count($tables) . "\n";
                
                foreach ($tables as $table) {
                    $count = $pdo->query("SELECT COUNT(*) FROM `$table`")->fetchColumn();
                    echo "表 $table: $count 条记录\n";
                }
                
                return true;
            } else {
                $pdo->rollBack();
                echo "❌ 错误太多，回滚事务: $backupFile\n";
            }
            
        } catch (Exception $e) {
            $pdo->rollBack();
            echo "❌ 恢复失败: " . $e->getMessage() . "\n";
        }
    }
    
    echo "\n所有备份文件都尝试过了，但都无法成功恢复。\n";
    return false;
    
} catch (Exception $e) {
    echo "❌ 数据库连接失败: " . $e->getMessage() . "\n";
    return false;
}
?>
