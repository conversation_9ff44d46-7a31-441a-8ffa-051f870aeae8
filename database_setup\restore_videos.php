<?php
/**
 * 恢复视频数据脚本
 * 从备份文件中提取并清理视频数据
 */

$host = 'mysql';
$username = 'root';
$password = 'root123456';
$database = 'zhengshiban_dev';

function cleanText($text) {
    // 移除乱码字符
    $text = preg_replace('/[^\x20-\x7E\x{4e00}-\x{9fff}]/u', '', $text);
    // 修复常见的编码问题
    $text = str_replace(['鈥', '€', '∶', '♀', '衡', '犆', 'β', '郝', '惷', 'ぢ', '好', '锯', '忙', '聽', '录', '氓', '聫', '莽', '拧', '灻', '猫', '碌', '樏'], '', $text);
    return trim($text);
}

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]);
    
    echo "数据库连接成功\n";
    
    // 读取备份文件
    $backupFile = '/var/www/html/data_only_backup.sql';
    if (!file_exists($backupFile)) {
        echo "备份文件不存在\n";
        exit(1);
    }
    
    $content = file_get_contents($backupFile);
    
    // 提取视频表的INSERT语句
    preg_match_all('/INSERT INTO `videos`.*?;/s', $content, $matches);
    
    if (empty($matches[0])) {
        echo "未找到视频数据\n";
        exit(1);
    }
    
    echo "找到 " . count($matches[0]) . " 条视频INSERT语句\n";
    
    // 清空现有的示例视频数据
    $pdo->exec("DELETE FROM videos WHERE id IN (1, 2, 3)");
    echo "清空示例数据\n";
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($matches[0] as $insertSql) {
        try {
            // 清理SQL语句
            $cleanSql = cleanText($insertSql);
            
            // 跳过空的或太短的语句
            if (strlen($cleanSql) < 50) {
                continue;
            }
            
            // 执行插入
            $pdo->exec($cleanSql);
            $successCount++;
            
            if ($successCount % 10 == 0) {
                echo "已恢复 $successCount 个视频...\n";
            }
            
        } catch (PDOException $e) {
            $errorCount++;
            if ($errorCount <= 5) { // 只显示前5个错误
                echo "错误: " . $e->getMessage() . "\n";
            }
        }
    }
    
    echo "\n恢复结果:\n";
    echo "成功: $successCount 个视频\n";
    echo "失败: $errorCount 个视频\n";
    
    // 验证恢复结果
    $totalVideos = $pdo->query("SELECT COUNT(*) FROM videos")->fetchColumn();
    echo "数据库中总视频数: $totalVideos\n";
    
    // 显示一些恢复的视频信息
    echo "\n恢复的视频示例:\n";
    $videos = $pdo->query("SELECT id, title, video_type, audit_status, created_at FROM videos ORDER BY id LIMIT 10")->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($videos as $video) {
        $title = cleanText($video['title']);
        echo "ID: {$video['id']}, 标题: $title, 类型: {$video['video_type']}, 状态: {$video['audit_status']}\n";
    }
    
    echo "\n🎉 视频数据恢复完成！\n";
    
} catch (Exception $e) {
    echo "❌ 恢复失败: " . $e->getMessage() . "\n";
}
?>
