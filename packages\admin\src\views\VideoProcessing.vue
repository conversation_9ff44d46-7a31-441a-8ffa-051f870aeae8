<template>
  <div class="video-processing-page">
    <div class="page-header">
      <h1>视频处理管理</h1>
      <p>管理FFmpeg进程和视频转码队列</p>
    </div>

    <!-- 系统状态卡片 -->
    <div class="status-cards">
      <div class="status-card">
        <div class="card-header">
          <h3>FFmpeg状态</h3>
          <div class="status-indicator" :class="{ active: status.ffmpeg_available }">
            {{ status.ffmpeg_available ? '可用' : '不可用' }}
          </div>
        </div>
      </div>

      <div class="status-card">
        <div class="card-header">
          <h3>队列工作进程</h3>
          <div class="status-indicator" :class="{ active: status.queue_worker_status?.running }">
            {{ status.queue_worker_status?.running ? '运行中' : '已停止' }}
          </div>
        </div>
        <div class="card-content" v-if="status.queue_worker_status?.pid">
          <p>PID: {{ status.queue_worker_status.pid }}</p>
        </div>
      </div>

      <div class="status-card">
        <div class="card-header">
          <h3>处理队列</h3>
        </div>
        <div class="card-content">
          <div class="queue-stats">
            <div class="stat-item">
              <span class="stat-label">等待中:</span>
              <span class="stat-value">{{ status.queue_stats?.pending || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">处理中:</span>
              <span class="stat-value">{{ status.queue_stats?.processing || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">已完成:</span>
              <span class="stat-value">{{ status.queue_stats?.completed || 0 }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">失败:</span>
              <span class="stat-value error">{{ status.queue_stats?.failed || 0 }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <h2>控制面板</h2>
      
      <div class="control-section">
        <h3>队列工作进程控制</h3>
        <div class="button-group">
          <button 
            class="btn btn-success" 
            @click="startWorker" 
            :disabled="loading || status.queue_worker_status?.running"
          >
            <i class="icon-play"></i> 启动进程
          </button>
          
          <button 
            class="btn btn-warning" 
            @click="restartWorker" 
            :disabled="loading"
          >
            <i class="icon-refresh"></i> 重启进程
          </button>
          
          <button 
            class="btn btn-danger" 
            @click="stopWorker" 
            :disabled="loading || !status.queue_worker_status?.running"
          >
            <i class="icon-stop"></i> 停止进程
          </button>
        </div>
      </div>

      <div class="control-section">
        <h3>队列管理</h3>
        <div class="button-group">
          <button 
            class="btn btn-primary" 
            @click="refreshStatus" 
            :disabled="loading"
          >
            <i class="icon-refresh"></i> 刷新状态
          </button>
          
          <button 
            class="btn btn-warning" 
            @click="clearQueue" 
            :disabled="loading"
          >
            <i class="icon-trash"></i> 清理队列
          </button>
          
          <button 
            class="btn btn-info" 
            @click="showBatchReprocess = true" 
            :disabled="loading"
          >
            <i class="icon-batch"></i> 批量重新处理
          </button>
        </div>
      </div>
    </div>

    <!-- 批量重新处理对话框 -->
    <div v-if="showBatchReprocess" class="modal-overlay" @click="showBatchReprocess = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>批量重新处理视频</h3>
          <button class="close-btn" @click="showBatchReprocess = false">&times;</button>
        </div>
        <div class="modal-body">
          <p>请输入要重新处理的视频ID（用逗号分隔）：</p>
          <textarea 
            v-model="batchVideoIds" 
            placeholder="例如: 1,2,3,4,5"
            rows="4"
            class="form-control"
          ></textarea>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" @click="showBatchReprocess = false">取消</button>
          <button class="btn btn-primary" @click="batchReprocess" :disabled="loading">
            开始处理
          </button>
        </div>
      </div>
    </div>

    <!-- 日志显示区域 -->
    <div class="log-section">
      <h2>操作日志</h2>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item" :class="log.type">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { api } from '@/utils/api'

// 响应式数据
const loading = ref(false)
const status = ref<any>({})
const logs = ref<any[]>([])
const showBatchReprocess = ref(false)
const batchVideoIds = ref('')

// 获取状态
const refreshStatus = async () => {
  try {
    loading.value = true
    const response = await api.get('/admin/video-processing/status')
    status.value = response.data
    addLog('状态刷新成功', 'success')
  } catch (error: any) {
    addLog('获取状态失败: ' + error.message, 'error')
  } finally {
    loading.value = false
  }
}

// 启动工作进程
const startWorker = async () => {
  try {
    loading.value = true
    await api.post('/admin/video-processing/start-worker')
    addLog('队列工作进程启动成功', 'success')
    await refreshStatus()
  } catch (error: any) {
    addLog('启动失败: ' + error.message, 'error')
  } finally {
    loading.value = false
  }
}

// 停止工作进程
const stopWorker = async () => {
  try {
    loading.value = true
    await api.post('/admin/video-processing/stop-worker')
    addLog('队列工作进程已停止', 'warning')
    await refreshStatus()
  } catch (error: any) {
    addLog('停止失败: ' + error.message, 'error')
  } finally {
    loading.value = false
  }
}

// 重启工作进程
const restartWorker = async () => {
  try {
    loading.value = true
    await api.post('/admin/video-processing/restart-worker')
    addLog('队列工作进程重启成功', 'success')
    await refreshStatus()
  } catch (error: any) {
    addLog('重启失败: ' + error.message, 'error')
  } finally {
    loading.value = false
  }
}

// 清理队列
const clearQueue = async () => {
  if (!confirm('确定要清理失败和超时的任务吗？')) return
  
  try {
    loading.value = true
    const response = await api.post('/admin/video-processing/clear-queue')
    addLog(`队列清理完成: 清理了 ${response.data.failed_count + response.data.timeout_count} 个任务`, 'success')
    await refreshStatus()
  } catch (error: any) {
    addLog('清理失败: ' + error.message, 'error')
  } finally {
    loading.value = false
  }
}

// 批量重新处理
const batchReprocess = async () => {
  if (!batchVideoIds.value.trim()) {
    addLog('请输入视频ID', 'error')
    return
  }
  
  try {
    loading.value = true
    const videoIds = batchVideoIds.value.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id))
    
    if (videoIds.length === 0) {
      addLog('请输入有效的视频ID', 'error')
      return
    }
    
    const response = await api.post('/admin/video-processing/batch-reprocess', {
      video_ids: videoIds
    })
    
    addLog(`批量处理提交成功: ${response.data.success_count} 个成功, ${response.data.failed_count} 个失败`, 'success')
    
    if (response.data.errors && response.data.errors.length > 0) {
      response.data.errors.forEach((error: string) => {
        addLog(error, 'error')
      })
    }
    
    showBatchReprocess.value = false
    batchVideoIds.value = ''
    await refreshStatus()
    
  } catch (error: any) {
    addLog('批量处理失败: ' + error.message, 'error')
  } finally {
    loading.value = false
  }
}

// 添加日志
const addLog = (message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    message,
    type
  })
  
  // 限制日志数量
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

// 页面加载时获取状态
onMounted(() => {
  refreshStatus()
  
  // 定时刷新状态
  setInterval(refreshStatus, 30000) // 每30秒刷新一次
})
</script>

<style scoped>
.video-processing-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 30px;
}

.page-header h1 {
  color: #333;
  margin-bottom: 5px;
}

.page-header p {
  color: #666;
  margin: 0;
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.status-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.card-header h3 {
  margin: 0;
  color: #333;
}

.status-indicator {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: bold;
  background: #f5f5f5;
  color: #999;
}

.status-indicator.active {
  background: #4CAF50;
  color: white;
}

.queue-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.stat-label {
  color: #666;
}

.stat-value {
  font-weight: bold;
  color: #333;
}

.stat-value.error {
  color: #f44336;
}

.control-panel {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.control-section {
  margin-bottom: 25px;
}

.control-section:last-child {
  margin-bottom: 0;
}

.control-section h3 {
  margin-bottom: 15px;
  color: #333;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  transition: all 0.3s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary { background: #2196F3; color: white; }
.btn-success { background: #4CAF50; color: white; }
.btn-warning { background: #FF9800; color: white; }
.btn-danger { background: #f44336; color: white; }
.btn-info { background: #00BCD4; color: white; }
.btn-secondary { background: #9E9E9E; color: white; }

.btn:hover:not(:disabled) {
  opacity: 0.9;
  transform: translateY(-1px);
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.form-control {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  resize: vertical;
}

.log-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  padding: 10px;
  background: #f9f9f9;
}

.log-item {
  display: flex;
  gap: 10px;
  padding: 5px 0;
  border-bottom: 1px solid #eee;
  font-family: monospace;
  font-size: 12px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #666;
  min-width: 80px;
}

.log-message {
  flex: 1;
}

.log-item.success .log-message { color: #4CAF50; }
.log-item.error .log-message { color: #f44336; }
.log-item.warning .log-message { color: #FF9800; }
.log-item.info .log-message { color: #2196F3; }
</style>
