<?php
/**
 * 简单的API测试脚本
 */

$apiUrl = 'http://localhost:3000/api/v1/videos?video_type=short&limit=5';
$apiKey = 'ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+=';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $apiUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'X-API-Key: ' . $apiKey
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP状态码: $httpCode\n";
if ($error) {
    echo "CURL错误: $error\n";
}
echo "响应内容:\n";
echo $response . "\n";

// 解析JSON
if ($response) {
    $data = json_decode($response, true);
    if ($data) {
        echo "\n解析后的数据:\n";
        echo "成功: " . ($data['success'] ? 'true' : 'false') . "\n";
        echo "消息: " . ($data['message'] ?? 'N/A') . "\n";
        if (isset($data['data']['list'])) {
            echo "视频数量: " . count($data['data']['list']) . "\n";
            foreach ($data['data']['list'] as $index => $video) {
                echo "视频" . ($index + 1) . ": ID={$video['id']}, 标题={$video['title']}, 类型={$video['video_type']}\n";
            }
        }
    }
}
?>
