<template>
  <div class="top-navigation">
    <!-- 左侧菜单按钮 -->
    <div class="nav-left">
      <div class="menu-button" @click="$emit('toggleMenu')">
        <svg viewBox="0 0 24 24" fill="currentColor" class="menu-icon">
          <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
        </svg>
      </div>
    </div>

    <!-- 中间导航标签 -->
    <div class="nav-center">
      <div class="nav-tabs">
        <div
          v-for="tab in tabs"
          :key="tab.key"
          class="nav-tab"
          :class="{ active: activeTab === tab.key }"
          @click="switchTab(tab.key)"
        >
          {{ tab.label }}
        </div>
      </div>
    </div>

    <!-- 右侧搜索按钮 -->
    <div class="nav-right">
      <div class="search-button" @click="$emit('toggleSearch')">
        <svg viewBox="0 0 24 24" fill="currentColor" class="search-icon">
          <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
        </svg>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Tab {
  key: string
  label: string
}

interface Props {
  tabs: Tab[]
  activeTab: string
}

interface Emits {
  (e: 'switchTab', tab: string): void
  (e: 'toggleMenu'): void
  (e: 'toggleSearch'): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

const switchTab = (tab: string) => {
  emit('switchTab', tab)
}
</script>

<style scoped>
/* 完全按照截图样式实现的顶部导航 */
.top-navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  /* 去掉底部边框线 */
  /* border-bottom: 1px solid #333333; */
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 1000;
  padding: 0 20px;
}

.nav-left {
  width: 40px;
  display: flex;
  align-items: center;
}

.menu-button {
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  transition: all 0.3s ease;
}

.menu-button:hover {
  color: #1989fa;
  transform: scale(1.1);
}

.menu-icon {
  width: 24px;
  height: 24px;
}

.nav-center {
  flex: 1;
  display: flex;
  justify-content: center;
  margin: 0 20px;
  padding: 0;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 25px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
  max-width: calc(100vw - 160px); /* 限制最大宽度 */
}

.nav-tabs {
  display: flex;
  gap: 20px;
  align-items: center;
  padding: 0 20px;
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scroll-behavior: smooth;
}

.nav-tabs::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

.nav-tab {
  padding: 8px 16px;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.7);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative;
  white-space: nowrap;
  border-radius: 0;
  background: transparent;
  border: none;
}

.nav-tab:hover {
  color: #ffffff;
}

.nav-tab.active {
  color: #ffffff;
  font-weight: 600;
  position: relative;
}

.nav-tab.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 50%;
  transform: translateX(-50%);
  width: 20px;
  height: 3px;
  background: #1989fa;
  border-radius: 2px;
}

.nav-right {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-right: 4px;
}

.search-button {
  width: 24px;
  height: 24px;
  color: #ffffff;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-button:hover {
  color: #1989fa;
  transform: scale(1.1);
}

.search-icon {
  width: 24px;
  height: 24px;
}



.logout-btn {
  color: #ff4757 !important;
}

.logout-btn:hover {
  background: rgba(255, 71, 87, 0.1) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .top-navigation {
    padding: 0 15px;
  }

  .nav-tabs {
    gap: 30px;
  }

  .nav-tab {
    font-size: 15px;
    padding: 6px 0;
  }
}

@media (max-width: 480px) {
  .top-navigation {
    padding: 0 12px;
  }

  .nav-tabs {
    gap: 20px;
  }

  .nav-tab {
    font-size: 14px;
    padding: 6px 0;
  }

  .menu-icon,
  .search-icon {
    width: 18px;
    height: 18px;
  }
}

@media (max-width: 360px) {
  .nav-tabs {
    gap: 15px;
  }

  .nav-tab {
    font-size: 13px;
  }
}
</style>
