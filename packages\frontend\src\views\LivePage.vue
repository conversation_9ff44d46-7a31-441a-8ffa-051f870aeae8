<template>
  <div class="live-page">
    <!-- 顶部导航 -->
    <div class="top-navigation">
      <div class="nav-left">
        <div class="hamburger-menu">
          <div class="hamburger-line"></div>
          <div class="hamburger-line"></div>
          <div class="hamburger-line"></div>
        </div>
      </div>
      
      <div class="nav-center">
        <h2 class="page-title">直播</h2>
      </div>
      
      <div class="nav-right">
        <div class="search-icon">🔍</div>
      </div>
    </div>

    <!-- 直播分类 -->
    <div class="live-categories">
      <div 
        v-for="category in categories" 
        :key="category.id"
        class="category-item"
        :class="{ active: activeCategory === category.id }"
        @click="activeCategory = category.id"
      >
        {{ category.name }}
      </div>
    </div>

    <!-- 直播列表 -->
    <div class="live-list">
      <div 
        v-for="live in filteredLives" 
        :key="live.id"
        class="live-card"
        @click="enterLive(live)"
      >
        <div class="live-thumbnail">
          <img :src="live.thumbnail" :alt="live.title">
          <div class="live-badge">直播中</div>
          <div class="viewer-count">{{ formatViewers(live.viewers) }}人观看</div>
        </div>
        
        <div class="live-info">
          <div class="streamer-info">
            <img :src="live.streamerAvatar" :alt="live.streamerName" class="streamer-avatar">
            <div class="streamer-details">
              <h3 class="live-title">{{ live.title }}</h3>
              <p class="streamer-name">{{ live.streamerName }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'


const activeCategory = ref('all')

// 直播分类
const categories = ref([
  { id: 'all', name: '全部' },
  { id: 'entertainment', name: '娱乐' },
  { id: 'game', name: '游戏' },
  { id: 'music', name: '音乐' },
  { id: 'dance', name: '舞蹈' },
  { id: 'chat', name: '聊天' }
])

// 生成虚拟图片URL的函数
const generateThumbnailUrl = (id: number, type: 'live' | 'avatar') => {
  const colors = ['FF6B6B', '4ECDC4', '45B7D1', 'FFA07A', '98D8C8', 'F7DC6F', 'BB8FCE', '85C1E9']
  const color = colors[id % colors.length]

  if (type === 'live') {
    // 直播间缩略图 - 使用可靠的placeholder服务
    const themes = ['游戏直播', '音乐表演', '聊天互动', '才艺展示', '户外探险']
    const theme = themes[id % themes.length]
    return `https://via.placeholder.com/320x180/${color}/FFFFFF?text=${encodeURIComponent(theme)}`
  } else {
    // 主播头像 - 使用可靠的头像服务
    const names = ['主播', '用户', '达人', '网红', '明星']
    const name = names[id % names.length]
    return `https://via.placeholder.com/80x80/${color}/FFFFFF?text=${encodeURIComponent(name + id)}`
  }
}

// 模拟直播数据
const lives = ref([
  {
    id: 1,
    title: '深夜聊天室 - 一起聊聊生活',
    thumbnail: generateThumbnailUrl(1, 'live'),
    streamerName: '主播小美',
    streamerAvatar: generateThumbnailUrl(1, 'avatar'),
    viewers: 1234,
    category: 'chat'
  },
  {
    id: 2,
    title: '王者荣耀上分局 - 带你飞',
    thumbnail: generateThumbnailUrl(2, 'live'),
    streamerName: '游戏大神',
    streamerAvatar: generateThumbnailUrl(2, 'avatar'),
    viewers: 5678,
    category: 'game'
  },
  {
    id: 3,
    title: '唱歌给你听 - 点歌台',
    thumbnail: generateThumbnailUrl(3, 'live'),
    streamerName: '歌手小雨',
    streamerAvatar: generateThumbnailUrl(3, 'avatar'),
    viewers: 2345,
    category: 'music'
  },
  {
    id: 4,
    title: '舞蹈教学 - 学会这支舞',
    thumbnail: generateThumbnailUrl(4, 'live'),
    streamerName: '舞蹈老师',
    streamerAvatar: generateThumbnailUrl(4, 'avatar'),
    viewers: 3456,
    category: 'dance'
  },
  {
    id: 5,
    title: '搞笑段子分享 - 笑到肚子疼',
    thumbnail: generateThumbnailUrl(5, 'live'),
    streamerName: '段子手',
    streamerAvatar: generateThumbnailUrl(5, 'avatar'),
    viewers: 4567,
    category: 'entertainment'
  },
  {
    id: 6,
    title: '美食制作直播 - 教你做菜',
    thumbnail: generateThumbnailUrl(6, 'live'),
    streamerName: '美食达人',
    streamerAvatar: generateThumbnailUrl(6, 'avatar'),
    viewers: 1890,
    category: 'entertainment'
  }
])

// 过滤直播列表
const filteredLives = computed(() => {
  if (activeCategory.value === 'all') {
    return lives.value
  }
  return lives.value.filter(live => live.category === activeCategory.value)
})

const enterLive = (live: any) => {
  console.log('进入直播间:', live.title)
  // 这里可以跳转到直播间页面
}

const formatViewers = (viewers: number) => {
  if (viewers >= 10000) {
    return `${(viewers / 10000).toFixed(1)}万`
  }
  return viewers.toLocaleString()
}
</script>

<style scoped>
.live-page {
  min-height: 100vh;
  background: #000000;
  color: #ffffff;
  padding-bottom: 60px;
}

/* 顶部导航 */
.top-navigation {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 50px;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 15px;
  z-index: 1000;
}

.nav-left, .nav-right {
  width: 50px;
  display: flex;
  justify-content: center;
}

.hamburger-menu {
  display: flex;
  flex-direction: column;
  gap: 3px;
  cursor: pointer;
}

.hamburger-line {
  width: 18px;
  height: 2px;
  background: #ffffff;
  border-radius: 1px;
}

.nav-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.page-title {
  font-size: 1.2rem;
  font-weight: 600;
}

.search-icon {
  color: #ffffff;
  font-size: 1.2rem;
  cursor: pointer;
}

/* 直播分类 */
.live-categories {
  position: fixed;
  top: 50px;
  left: 0;
  right: 0;
  height: 50px;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  padding: 0 15px;
  gap: 20px;
  overflow-x: auto;
  z-index: 999;
}

.category-item {
  white-space: nowrap;
  padding: 8px 16px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  color: #cccccc;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-item.active {
  background: #ff4444;
  color: #ffffff;
}

.category-item:hover {
  background: rgba(255, 68, 68, 0.3);
}

/* 直播列表 */
.live-list {
  padding: 110px 15px 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 15px;
}

.live-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.live-card:hover {
  transform: translateY(-3px);
  background: rgba(255, 255, 255, 0.1);
}

.live-thumbnail {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;
}

.live-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.live-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #ff4444;
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
}

.viewer-count {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.8);
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.live-info {
  padding: 12px;
}

.streamer-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.streamer-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  flex-shrink: 0;
}

.streamer-details {
  flex: 1;
  min-width: 0;
}

.live-title {
  font-size: 0.9rem;
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 4px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.streamer-name {
  font-size: 0.8rem;
  color: #cccccc;
  margin: 0;
}

@media (max-width: 768px) {
  .live-list {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    padding: 110px 10px 20px;
  }
}

@media (max-width: 480px) {
  .live-list {
    grid-template-columns: 1fr;
  }
  
  .live-categories {
    padding: 0 10px;
    gap: 15px;
  }
  
  .category-item {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}
</style>
