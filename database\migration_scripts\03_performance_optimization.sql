-- =====================================================
-- 性能优化脚本
-- 优化数据库索引和查询性能
-- 执行时间: 2025-01-27
-- =====================================================

-- 1. 为videos表添加性能优化索引
SELECT 'Creating performance indexes for videos table' as operation;

-- 为常用查询字段添加索引
CREATE INDEX IF NOT EXISTS idx_videos_status_audit ON videos(status, audit_status);
CREATE INDEX IF NOT EXISTS idx_videos_source_type ON videos(source_type);
CREATE INDEX IF NOT EXISTS idx_videos_category_status ON videos(category_id, status);
CREATE INDEX IF NOT EXISTS idx_videos_user_status ON videos(user_id, status);
CREATE INDEX IF NOT EXISTS idx_videos_created_status ON videos(created_at, status);
CREATE INDEX IF NOT EXISTS idx_videos_view_count ON videos(view_count DESC);
CREATE INDEX IF NOT EXISTS idx_videos_like_count ON videos(like_count DESC);

-- 为采集相关字段添加索引
CREATE INDEX IF NOT EXISTS idx_videos_collect_source ON videos(collect_source_id);
CREATE INDEX IF NOT EXISTS idx_videos_remote_id ON videos(remote_video_id);

-- 为播放地址相关字段添加索引
CREATE INDEX IF NOT EXISTS idx_videos_play_urls ON videos(vod_play_url(100));

-- 2. 为video_collections表添加性能优化索引
SELECT 'Creating performance indexes for video_collections table' as operation;

-- 复合索引优化收藏查询
CREATE INDEX IF NOT EXISTS idx_collections_user_created ON video_collections(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_collections_video_created ON video_collections(video_id, created_at DESC);

-- 3. 为video_extended_info表添加性能优化索引
SELECT 'Creating performance indexes for video_extended_info table' as operation;

-- 为常用筛选字段添加索引
CREATE INDEX IF NOT EXISTS idx_extended_area_year ON video_extended_info(vod_area, vod_year);
CREATE INDEX IF NOT EXISTS idx_extended_lang_year ON video_extended_info(vod_lang, vod_year);
CREATE INDEX IF NOT EXISTS idx_extended_hits_day ON video_extended_info(vod_hits_day DESC);
CREATE INDEX IF NOT EXISTS idx_extended_hits_week ON video_extended_info(vod_hits_week DESC);
CREATE INDEX IF NOT EXISTS idx_extended_hits_month ON video_extended_info(vod_hits_month DESC);
CREATE INDEX IF NOT EXISTS idx_extended_score ON video_extended_info(vod_douban_score DESC);
CREATE INDEX IF NOT EXISTS idx_extended_isend ON video_extended_info(vod_isend);

-- 4. 为其他关键表添加索引
SELECT 'Creating indexes for other critical tables' as operation;

-- users表索引优化
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_users_created ON users(created_at);

-- categories表索引优化
CREATE INDEX IF NOT EXISTS idx_categories_parent ON categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_categories_status ON categories(status);
CREATE INDEX IF NOT EXISTS idx_categories_sort ON categories(sort_order);

-- 5. 创建复合索引优化复杂查询
SELECT 'Creating composite indexes for complex queries' as operation;

-- 视频列表查询优化
CREATE INDEX IF NOT EXISTS idx_videos_list_query ON videos(status, audit_status, category_id, created_at DESC);

-- 用户视频查询优化
CREATE INDEX IF NOT EXISTS idx_videos_user_query ON videos(user_id, status, created_at DESC);

-- 热门视频查询优化
CREATE INDEX IF NOT EXISTS idx_videos_hot_query ON videos(status, view_count DESC, created_at DESC);

-- 搜索优化（全文索引）
-- 注意：全文索引需要MyISAM或InnoDB引擎支持
ALTER TABLE videos ADD FULLTEXT(title, description) IF NOT EXISTS;

-- 6. 分析表统计信息
SELECT 'Analyzing table statistics' as operation;

ANALYZE TABLE videos;
ANALYZE TABLE video_collections;
ANALYZE TABLE video_extended_info;
ANALYZE TABLE users;
ANALYZE TABLE categories;

-- 7. 优化表结构
SELECT 'Optimizing table structure' as operation;

OPTIMIZE TABLE videos;
OPTIMIZE TABLE video_collections;
OPTIMIZE TABLE video_extended_info;

-- 8. 创建视图优化常用查询
SELECT 'Creating optimized views' as operation;

-- 创建视频详情视图
CREATE OR REPLACE VIEW video_details AS
SELECT 
    v.*,
    vei.vod_area,
    vei.vod_year,
    vei.vod_lang,
    vei.vod_douban_score,
    vei.vod_hits_day,
    vei.vod_hits_week,
    vei.vod_hits_month,
    c.name as category_name,
    u.username as uploader_name
FROM videos v
LEFT JOIN video_extended_info vei ON v.id = vei.video_id
LEFT JOIN categories c ON v.category_id = c.id
LEFT JOIN users u ON v.user_id = u.id
WHERE v.status = 'published' AND v.audit_status = 'approved';

-- 创建热门视频视图
CREATE OR REPLACE VIEW hot_videos AS
SELECT 
    v.id,
    v.title,
    v.cover_image,
    v.view_count,
    v.like_count,
    v.created_at,
    vei.vod_area,
    vei.vod_year,
    vei.vod_douban_score
FROM videos v
LEFT JOIN video_extended_info vei ON v.id = vei.video_id
WHERE v.status = 'published' 
AND v.audit_status = 'approved'
ORDER BY v.view_count DESC, v.created_at DESC;

-- 创建用户收藏视图
CREATE OR REPLACE VIEW user_collections AS
SELECT 
    vc.user_id,
    vc.video_id,
    vc.created_at as collected_at,
    v.title,
    v.cover_image,
    v.duration,
    v.view_count
FROM video_collections vc
JOIN videos v ON vc.video_id = v.id
WHERE v.status = 'published' AND v.audit_status = 'approved';

-- 9. 显示索引创建结果
SELECT 'Performance optimization summary' as operation;

-- 显示videos表的索引
SELECT 
    'videos table indexes' as table_name,
    index_name,
    column_name,
    seq_in_index,
    cardinality
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND table_name = 'videos'
ORDER BY index_name, seq_in_index;

-- 显示video_extended_info表的索引
SELECT 
    'video_extended_info table indexes' as table_name,
    index_name,
    column_name,
    seq_in_index,
    cardinality
FROM information_schema.statistics 
WHERE table_schema = DATABASE() 
AND table_name = 'video_extended_info'
ORDER BY index_name, seq_in_index;

-- 显示表大小统计
SELECT
    'Table size statistics' as info,
    table_name,
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb,
    table_rows,
    ROUND((index_length / 1024 / 1024), 2) as index_size_mb
FROM information_schema.tables
WHERE table_schema = DATABASE()
AND table_name IN ('videos', 'video_collections', 'video_extended_info', 'users', 'categories')
ORDER BY size_mb DESC;

-- 10. 高级索引优化
SELECT 'Advanced index optimization' as operation;

-- 创建复合索引优化常见查询
CREATE INDEX IF NOT EXISTS idx_videos_status_audit_created ON videos(status, audit_status, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_videos_category_status_views ON videos(category_id, status, view_count DESC);
CREATE INDEX IF NOT EXISTS idx_videos_user_status_created ON videos(user_id, status, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_videos_type_status_created ON videos(video_type, status, created_at DESC);

-- 用户相关复合索引
CREATE INDEX IF NOT EXISTS idx_users_status_created ON users(status, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_users_email_status ON users(email, status);

-- 收藏相关复合索引
CREATE INDEX IF NOT EXISTS idx_collections_user_created ON video_collections(user_id, created_at DESC);

-- 评论相关复合索引
CREATE INDEX IF NOT EXISTS idx_comments_video_status_created ON video_comments(video_id, status, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_comments_user_status_created ON video_comments(user_id, status, created_at DESC);

-- 点赞相关复合索引
CREATE INDEX IF NOT EXISTS idx_likes_video_created ON video_likes(video_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_likes_user_created ON video_likes(user_id, created_at DESC);

-- 采集相关复合索引
CREATE INDEX IF NOT EXISTS idx_collect_tasks_source_status ON collect_tasks(source_id, status);
CREATE INDEX IF NOT EXISTS idx_collect_logs_task_status ON collect_logs(task_id, status);

-- 11. 分区表优化 (如果数据量大)
SELECT 'Partition optimization suggestions' as operation;

-- 为大表创建分区建议 (注释形式，需要根据实际情况执行)
/*
-- 按月分区videos表 (适用于数据量超过100万的情况)
ALTER TABLE videos PARTITION BY RANGE (YEAR(created_at) * 100 + MONTH(created_at)) (
    PARTITION p202401 VALUES LESS THAN (202402),
    PARTITION p202402 VALUES LESS THAN (202403),
    PARTITION p202403 VALUES LESS THAN (202404),
    PARTITION p202404 VALUES LESS THAN (202405),
    PARTITION p202405 VALUES LESS THAN (202406),
    PARTITION p202406 VALUES LESS THAN (202407),
    PARTITION p202407 VALUES LESS THAN (202408),
    PARTITION p202408 VALUES LESS THAN (202409),
    PARTITION p202409 VALUES LESS THAN (202410),
    PARTITION p202410 VALUES LESS THAN (202411),
    PARTITION p202411 VALUES LESS THAN (202412),
    PARTITION p202412 VALUES LESS THAN (202501),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
*/

-- 12. 查询优化建议
SELECT 'Query optimization recommendations' as operation;

-- 创建查询优化视图
CREATE OR REPLACE VIEW query_performance_stats AS
SELECT
    'Query Performance Statistics' as info,
    (SELECT COUNT(*) FROM videos WHERE status = 'published') as published_videos,
    (SELECT COUNT(*) FROM users WHERE status = 'active') as active_users,
    (SELECT COUNT(*) FROM video_collections) as total_collections,
    (SELECT COUNT(*) FROM video_comments WHERE status = 'approved') as approved_comments,
    (SELECT AVG(view_count) FROM videos WHERE status = 'published') as avg_views_per_video,
    (SELECT MAX(view_count) FROM videos) as max_views,
    (SELECT COUNT(*) FROM videos WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)) as videos_last_7_days;

-- 13. 慢查询监控设置
SELECT 'Slow query monitoring setup' as operation;

-- 显示当前慢查询设置
SELECT
    'Current slow query settings' as info,
    @@slow_query_log as slow_query_log_enabled,
    @@long_query_time as slow_query_threshold_seconds,
    @@log_queries_not_using_indexes as log_queries_not_using_indexes;

-- 建议的慢查询设置 (需要管理员权限)
/*
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 1;
SET GLOBAL log_queries_not_using_indexes = 'ON';
*/

-- 14. 内存优化建议
SELECT 'Memory optimization recommendations' as operation;

-- 显示当前内存设置
SELECT
    'Current memory settings' as info,
    @@innodb_buffer_pool_size as innodb_buffer_pool_size,
    @@query_cache_size as query_cache_size,
    @@tmp_table_size as tmp_table_size,
    @@max_heap_table_size as max_heap_table_size;

SELECT 'Performance optimization completed successfully' as status;
