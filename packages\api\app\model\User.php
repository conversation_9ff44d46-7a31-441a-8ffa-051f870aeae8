<?php
namespace app\model;
use think\Model;
class User extends Model
{
    protected $name = "users";
    protected $autoWriteTimestamp = true;
    protected $hidden = ["password"];
    
    public function setPasswordAttr($value)
    {
        return password_hash($value, PASSWORD_DEFAULT);
    }
    
    public function verifyPassword($password)
    {
        return password_verify($password, $this->password);
    }
    
    public static function findByUsername($username)
    {
        return self::where("username", $username)->find();
    }

    public static function findByEmail($email)
    {
        return self::where("email", $email)->find();
    }
    
    public static function usernameExists($username)
    {
        return self::where("username", $username)->count() > 0;
    }
    
    public static function emailExists($email)
    {
        return self::where("email", $email)->count() > 0;
    }
    
    public static function createUser($data)
    {
        try {
            $user = new self();
            $user->username = $data["username"];
            $user->email = $data["email"];
            $user->password = $data["password"];
            $user->nickname = $data["nickname"] ?? $data["username"];
            $user->status = 'active'; // 激活状态

            $result = $user->save();

            if ($result) {
                error_log("用户创建成功: " . $data["username"] . " (ID: " . $user->id . ")");
                return $user;
            } else {
                error_log("用户保存失败: " . $data["username"] . " - save()返回false");
                return false;
            }
        } catch (\Exception $e) {
            error_log("用户创建异常: " . $data["username"] . " - " . $e->getMessage());
            return false;
        }
    }
    
    public function getBasicInfo()
    {
        return [
            "id" => $this->id,
            "username" => $this->username,
            "email" => $this->email,
            "nickname" => $this->nickname,
            "status" => $this->status,
            "created_at" => $this->created_at
        ];
    }
    
    public function isActive()
    {
        return $this->status === 'active'; // 激活状态
    }
}
