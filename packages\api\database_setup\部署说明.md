# 采集模块数据库部署说明

## 📋 部署概述
本文档说明如何在服务器上部署采集模块的数据库结构。

## 🗂️ 文件清单
- `采集模块数据库需求文档.md` - 详细的数据库需求文档
- `采集模块完整数据库.sql` - 完整的SQL创建脚本
- `部署说明.md` - 本部署说明文档

## 🚀 部署步骤

### 1. 上传文件到服务器
将 `database_setup` 文件夹上传到服务器的项目目录中。

### 2. 连接数据库
```bash
# 方式1: 直接连接MySQL
mysql -u root -p

# 方式2: 如果使用Docker
docker exec -it mysql容器名 mysql -u root -p
```

### 3. 选择数据库
```sql
USE your_database_name;
```

### 4. 执行SQL脚本
```bash
# 在服务器上执行
mysql -u root -p your_database_name < database_setup/采集模块完整数据库.sql

# 或者在MySQL命令行中执行
source /path/to/database_setup/采集模块完整数据库.sql;
```

### 5. 验证部署结果
```sql
-- 检查表是否创建成功
SHOW TABLES LIKE 'collect_%';

-- 检查数据是否插入成功
SELECT 'collect_sources' as table_name, COUNT(*) as record_count FROM collect_sources
UNION ALL
SELECT 'collect_config' as table_name, COUNT(*) as record_count FROM collect_config;
```

## 📊 预期结果

### 创建的表
- `collect_sources` - 采集源配置表
- `collect_category_mapping` - 分类映射表  
- `collect_tasks` - 采集任务表
- `collect_logs` - 采集日志表
- `collect_duplicates` - 采集去重表
- `collect_bind` - 采集绑定表
- `collect_statistics` - 采集统计表
- `collect_config` - 采集配置表

### 初始数据
- `collect_sources`: 2条测试采集源记录
- `collect_config`: 6条基础配置记录

## 🔧 配置说明

### 环境变量检查
确保以下环境变量正确配置：
```env
DB_HOST=mysql
DB_NAME=your_database_name
DB_USER=your_username
DB_PASS=your_password
```

### API密钥配置
确保API密钥配置正确：
```env
API_KEY_ADMIN=your_admin_api_key
```

## 🧪 测试验证

### 1. API连接测试
```bash
# 登录获取token
curl -X POST http://your-domain/api/admin/login \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key" \
  -d '{"username":"admin","password":"your_password"}'

# 测试采集源API
curl -X GET "http://your-domain/api/admin/collect/sources?page=1&limit=20" \
  -H "X-API-Key: your_api_key" \
  -H "Authorization: Bearer your_token"
```

### 2. 预期响应
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "测试采集源",
        "api_url": "https://api.example.com/collect",
        "api_type": "json",
        "status": 1
      }
    ],
    "total": 2,
    "page": 1,
    "limit": 20,
    "pages": 1
  }
}
```

## ⚠️ 注意事项

### 1. 数据库权限
确保数据库用户有以下权限：
- CREATE - 创建表
- INSERT - 插入数据
- SELECT - 查询数据
- UPDATE - 更新数据
- DELETE - 删除数据
- INDEX - 创建索引
- REFERENCES - 外键约束

### 2. 字符集设置
确保数据库使用 `utf8mb4` 字符集：
```sql
ALTER DATABASE your_database_name CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. 外键约束
部分表有外键约束，确保相关表已存在：
- `video_categories` 表
- `videos` 表

### 4. 存储引擎
所有表使用 InnoDB 存储引擎，支持事务和外键约束。

## 🔍 故障排除

### 常见问题
1. **外键约束失败**: 确保被引用的表已存在
2. **字符集问题**: 检查数据库和表的字符集设置
3. **权限不足**: 检查数据库用户权限
4. **表已存在**: 使用 `IF NOT EXISTS` 避免重复创建

### 日志检查
```bash
# 检查MySQL错误日志
tail -f /var/log/mysql/error.log

# 检查应用日志
tail -f /path/to/your/app/logs/error.log
```

## 📞 技术支持
如遇到部署问题，请检查：
1. 数据库连接配置
2. 环境变量设置
3. API密钥配置
4. 表结构完整性

部署完成后，采集模块的所有功能应该可以正常使用。
