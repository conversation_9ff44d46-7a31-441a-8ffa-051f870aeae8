<?php
declare(strict_types=1);

namespace app\service;

use think\facade\Db;
use think\facade\Log;
use think\facade\Cache;

/**
 * 统一数据库服务
 * 
 * 整合所有数据库相关功能：
 * - 连接管理（读写分离、连接池）
 * - 查询优化（慢查询检测、N+1查询避免）
 * - 性能监控（查询统计、执行时间）
 * - 索引管理（索引建议、自动优化）
 * - 事务管理（嵌套事务、回滚处理）
 */
class DatabaseService
{
    /**
     * 默认连接名
     */
    private string $defaultConnection = 'mysql';

    /**
     * 查询统计
     */
    private array $queryStats = [
        'read_count' => 0,
        'write_count' => 0,
        'read_time' => 0,
        'write_time' => 0,
        'slow_queries' => 0
    ];

    /**
     * 慢查询阈值（秒）
     */
    private float $slowQueryThreshold = 1.0;

    /**
     * 缓存配置
     */
    private array $cacheConfig = [
        'stats_key' => 'db_stats',
        'optimization_key' => 'db_optimization',
        'ttl' => 3600
    ];

    /**
     * 查询优化配置
     */
    private array $optimizationConfig = [
        'enable_query_cache' => true,
        'enable_explain_analysis' => true,
        'enable_index_hints' => true,
        'max_query_cache_size' => 1000,
        'query_cache_ttl' => 300,
    ];

    /**
     * 索引建议缓存
     */
    private array $indexSuggestions = [];

    /**
     * 事务嵌套级别
     */
    private int $transactionLevel = 0;

    public function __construct()
    {
        Log::info('数据库服务初始化', [
            'connection' => $this->defaultConnection,
            'slow_query_threshold' => $this->slowQueryThreshold
        ]);
    }

    /**
     * 获取数据库连接
     */
    public function getConnection()
    {
        return Db::connect($this->defaultConnection);
    }

    /**
     * 获取读连接
     */
    public function getReadConnection()
    {
        $this->queryStats['read_count']++;
        return $this->getConnection();
    }

    /**
     * 获取写连接
     */
    public function getWriteConnection()
    {
        $this->queryStats['write_count']++;
        return $this->getConnection();
    }

    /**
     * 执行读查询
     */
    public function table(string $table)
    {
        return $this->getReadConnection()->table($table);
    }

    /**
     * 执行写查询
     */
    public function writeTable(string $table)
    {
        return $this->getWriteConnection()->table($table);
    }

    /**
     * 执行原生SQL查询
     */
    public function query(string $sql, array $bind = [], bool $isWrite = false)
    {
        $startTime = microtime(true);

        try {
            $result = $this->getConnection()->query($sql, $bind);
            $executionTime = microtime(true) - $startTime;

            // 更新统计
            if ($isWrite) {
                $this->queryStats['write_time'] += $executionTime;
            } else {
                $this->queryStats['read_time'] += $executionTime;
            }

            // 检查慢查询
            if ($executionTime > $this->slowQueryThreshold) {
                $this->queryStats['slow_queries']++;
                $this->logSlowQuery($sql, $bind, $executionTime, $isWrite);
            }

            Log::info('SQL查询执行', [
                'sql' => substr($sql, 0, 200),
                'bind_count' => count($bind),
                'is_write' => $isWrite,
                'execution_time' => round($executionTime, 3)
            ]);

            return $result;
        } catch (\Exception $e) {
            $executionTime = microtime(true) - $startTime;
            Log::error('SQL查询失败', [
                'sql' => substr($sql, 0, 200),
                'bind_count' => count($bind),
                'is_write' => $isWrite,
                'error' => $e->getMessage(),
                'execution_time' => round($executionTime, 3)
            ]);
            throw $e;
        }
    }

    /**
     * 优化的视频列表查询
     */
    public function getOptimizedVideoList(array $params = []): array
    {
        $startTime = microtime(true);
        
        // 使用优化的查询，避免不必要的JOIN
        $baseQuery = $this->table('videos')->alias('v');
        
        // 只在需要时才JOIN用户表和分类表
        $needUserInfo = !empty($params['include_user']);
        $needCategoryInfo = !empty($params['include_category']);
        
        if ($needUserInfo) {
            $baseQuery->leftJoin('users u', 'v.user_id = u.id');
        }
        
        if ($needCategoryInfo) {
            $baseQuery->leftJoin('video_categories c', 'v.category_id = c.id');
        }
        
        // 基础字段
        $fields = [
            'v.id', 'v.title', 'v.description', 'v.cover_image', 
            'v.duration', 'v.view_count', 'v.like_count', 'v.is_vip',
            'v.created_at', 'v.category_id', 'v.user_id'
        ];
        
        // 根据需要添加额外字段
        if ($needUserInfo) {
            $fields = array_merge($fields, ['u.username', 'u.nickname', 'u.avatar as user_avatar']);
        }
        
        if ($needCategoryInfo) {
            $fields[] = 'c.name as category_name';
        }
        
        $baseQuery->field($fields);
        
        // 基础过滤条件
        $baseQuery->where([
            ['v.status', '=', 'published'],
            ['v.audit_status', '=', 'approved']
        ]);
        
        // 动态条件
        if (!empty($params['category_id'])) {
            $baseQuery->where('v.category_id', $params['category_id']);
        }
        
        if (!empty($params['video_type'])) {
            $baseQuery->where('v.video_type', $params['video_type']);
        }
        
        if (!empty($params['keyword'])) {
            $baseQuery->where('v.title', 'like', "%{$params['keyword']}%");
        }
        
        if (!empty($params['is_vip'])) {
            $baseQuery->where('v.is_vip', $params['is_vip']);
        }
        
        // 分页参数
        $page = max(1, (int)($params['page'] ?? 1));
        $limit = min((int)($params['limit'] ?? 20), 50);
        $offset = ($page - 1) * $limit;
        
        // 优化的计数查询 - 避免复杂JOIN
        $countQuery = $this->table('videos')
            ->where([
                ['status', '=', 'published'],
                ['audit_status', '=', 'approved']
            ]);
            
        if (!empty($params['category_id'])) {
            $countQuery->where('category_id', $params['category_id']);
        }
        
        if (!empty($params['video_type'])) {
            $countQuery->where('video_type', $params['video_type']);
        }
        
        if (!empty($params['keyword'])) {
            $countQuery->where('title', 'like', "%{$params['keyword']}%");
        }
        
        $total = $countQuery->count();
        
        // 数据查询 - 使用优化的排序
        $orderBy = $params['order_by'] ?? 'created_at';
        $orderDir = $params['order_dir'] ?? 'desc';
        
        // 确保排序字段有索引
        $allowedOrderFields = ['created_at', 'view_count', 'like_count', 'id'];
        if (!in_array($orderBy, $allowedOrderFields)) {
            $orderBy = 'created_at';
        }
        
        $videos = $baseQuery->order("v.{$orderBy}", $orderDir)
            ->limit($offset, $limit)
            ->select()
            ->toArray();
        
        $queryTime = microtime(true) - $startTime;
        
        // 记录慢查询
        if ($queryTime > $this->slowQueryThreshold) {
            Log::warning("慢查询检测", [
                'query_type' => 'video_list',
                'query_time' => $queryTime,
                'params' => $params,
                'total_records' => $total
            ]);
        }
        
        return [
            'videos' => $videos,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit),
            'query_time' => $queryTime
        ];
    }

    /**
     * 获取优化的用户统计信息
     */
    public function getOptimizedUserStats(int $userId): array
    {
        $startTime = microtime(true);
        
        // 使用单个查询获取所有统计信息
        $stats = $this->query("
            SELECT 
                COUNT(CASE WHEN v.status = 'published' THEN 1 END) as video_count,
                COALESCE(SUM(v.view_count), 0) as total_views,
                COALESCE(SUM(v.like_count), 0) as total_likes,
                (SELECT COUNT(*) FROM video_collections WHERE user_id = ?) as favorite_count
            FROM videos v 
            WHERE v.user_id = ?
        ", [$userId, $userId]);
        
        $result = $stats[0] ?? [
            'video_count' => 0,
            'total_views' => 0,
            'total_likes' => 0,
            'favorite_count' => 0
        ];
        
        $queryTime = microtime(true) - $startTime;
        
        if ($queryTime > 0.1) {
            Log::warning("用户统计查询较慢", [
                'user_id' => $userId,
                'query_time' => $queryTime
            ]);
        }
        
        return $result;
    }

    /**
     * 开始事务
     */
    public function startTrans(): void
    {
        if ($this->transactionLevel === 0) {
            $this->getConnection()->startTrans();
            Log::info('开始数据库事务');
        }
        $this->transactionLevel++;
    }

    /**
     * 提交事务
     */
    public function commit(): void
    {
        $this->transactionLevel--;
        if ($this->transactionLevel === 0) {
            $this->getConnection()->commit();
            Log::info('提交数据库事务');
        } elseif ($this->transactionLevel < 0) {
            $this->transactionLevel = 0;
            Log::warning('事务嵌套级别异常，已重置');
        }
    }

    /**
     * 回滚事务
     */
    public function rollback(): void
    {
        if ($this->transactionLevel > 0) {
            $this->getConnection()->rollback();
            Log::info('回滚数据库事务');
        }
        $this->transactionLevel = 0;
    }

    /**
     * 执行事务
     */
    public function transaction(callable $callback)
    {
        $this->startTrans();
        try {
            $result = $callback($this);
            $this->commit();
            return $result;
        } catch (\Exception $e) {
            $this->rollback();
            throw $e;
        }
    }

    /**
     * 批量插入数据
     */
    public function batchInsert(string $table, array $data, int $batchSize = 1000): bool
    {
        if (empty($data)) {
            return true;
        }

        $chunks = array_chunk($data, $batchSize);
        $success = true;

        foreach ($chunks as $chunk) {
            try {
                $this->writeTable($table)->insertAll($chunk);
            } catch (\Exception $e) {
                Log::error("批量插入失败", [
                    'table' => $table,
                    'chunk_size' => count($chunk),
                    'error' => $e->getMessage()
                ]);
                $success = false;
            }
        }

        return $success;
    }

    /**
     * 获取数据库状态
     */
    public function getDatabaseStatus(): array
    {
        try {
            $status = $this->query("SHOW STATUS WHERE Variable_name IN (
                'Connections', 'Threads_connected', 'Threads_running',
                'Queries', 'Slow_queries', 'Uptime',
                'Innodb_buffer_pool_size', 'Innodb_buffer_pool_pages_free',
                'Innodb_buffer_pool_pages_total'
            )");

            $formatted = [];
            foreach ($status as $item) {
                $formatted[$item['Variable_name']] = $item['Value'];
            }

            return $formatted;
        } catch (\Exception $e) {
            Log::error("获取数据库状态失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 分析慢查询并提供优化建议
     */
    public function analyzeSlowQueries(): array
    {
        try {
            // 获取慢查询日志（需要开启慢查询日志）
            $slowQueries = $this->query("
                SELECT * FROM mysql.slow_log
                WHERE start_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
                ORDER BY start_time DESC
                LIMIT 10
            ");

            $suggestions = [];

            foreach ($slowQueries as $query) {
                $suggestions[] = [
                    'query' => $query['sql_text'],
                    'query_time' => $query['query_time'],
                    'suggestion' => $this->generateOptimizationSuggestion($query['sql_text'])
                ];
            }

            return $suggestions;

        } catch (\Exception $e) {
            Log::error("分析慢查询失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 检查和创建推荐的索引
     */
    public function checkAndCreateIndexes(): array
    {
        $recommendations = [];

        try {
            // 检查videos表的索引
            $videoIndexes = $this->checkVideoTableIndexes();
            $recommendations = array_merge($recommendations, $videoIndexes);

            // 检查users表的索引
            $userIndexes = $this->checkUserTableIndexes();
            $recommendations = array_merge($recommendations, $userIndexes);

            // 检查其他表的索引
            $otherIndexes = $this->checkOtherTableIndexes();
            $recommendations = array_merge($recommendations, $otherIndexes);

        } catch (\Exception $e) {
            Log::error("检查索引失败: " . $e->getMessage());
        }

        return $recommendations;
    }

    /**
     * 获取查询统计信息
     */
    public function getQueryStats(): array
    {
        return array_merge($this->queryStats, [
            'avg_read_time' => $this->queryStats['read_count'] > 0
                ? round($this->queryStats['read_time'] / $this->queryStats['read_count'], 3)
                : 0,
            'avg_write_time' => $this->queryStats['write_count'] > 0
                ? round($this->queryStats['write_time'] / $this->queryStats['write_count'], 3)
                : 0,
            'slow_query_rate' => $this->queryStats['read_count'] + $this->queryStats['write_count'] > 0
                ? round(($this->queryStats['slow_queries'] / ($this->queryStats['read_count'] + $this->queryStats['write_count'])) * 100, 2)
                : 0
        ]);
    }

    /**
     * 重置查询统计
     */
    public function resetQueryStats(): void
    {
        $this->queryStats = [
            'read_count' => 0,
            'write_count' => 0,
            'read_time' => 0,
            'write_time' => 0,
            'slow_queries' => 0
        ];
        Log::info('查询统计已重置');
    }

    /**
     * 检查数据库连接状态
     */
    public function checkConnectionStatus(): array
    {
        return [
            'default' => $this->isConnectionAvailable($this->defaultConnection),
            'transaction_level' => $this->transactionLevel
        ];
    }

    /**
     * 记录慢查询
     */
    private function logSlowQuery(string $sql, array $bind, float $executionTime, bool $isWrite): void
    {
        Log::warning('慢查询检测', [
            'sql' => substr($sql, 0, 500),
            'bind_count' => count($bind),
            'execution_time' => round($executionTime, 3),
            'is_write' => $isWrite,
            'suggestion' => $this->generateOptimizationSuggestion($sql)
        ]);
    }

    /**
     * 检查连接是否可用
     */
    private function isConnectionAvailable(string $connection): bool
    {
        try {
            $db = Db::connect($connection);
            $db->query('SELECT 1');
            return true;
        } catch (\Exception $e) {
            Log::error('数据库连接检查失败', [
                'connection' => $connection,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 生成优化建议
     */
    private function generateOptimizationSuggestion(string $query): string
    {
        $query = strtolower($query);

        if (strpos($query, 'select *') !== false) {
            return '避免使用SELECT *，只查询需要的字段';
        }

        if (strpos($query, 'like %') !== false) {
            return '考虑使用全文索引替代LIKE查询';
        }

        if (strpos($query, 'order by') !== false && strpos($query, 'limit') === false) {
            return '大结果集排序时建议添加LIMIT限制';
        }

        if (strpos($query, 'join') !== false) {
            return '检查JOIN条件是否有适当的索引';
        }

        return '建议分析查询执行计划并优化索引';
    }

    /**
     * 检查videos表索引
     */
    private function checkVideoTableIndexes(): array
    {
        $recommendations = [];

        try {
            $indexes = $this->query("SHOW INDEX FROM videos");
            $existingIndexes = array_column($indexes, 'Column_name');

            $recommendedIndexes = [
                'category_id' => '分类查询优化',
                'user_id' => '用户视频查询优化',
                'status' => '状态过滤优化',
                'created_at' => '时间排序优化',
                'view_count' => '热门排序优化'
            ];

            foreach ($recommendedIndexes as $column => $description) {
                if (!in_array($column, $existingIndexes)) {
                    $recommendations[] = [
                        'table' => 'videos',
                        'column' => $column,
                        'type' => 'index',
                        'description' => $description,
                        'sql' => "ALTER TABLE videos ADD INDEX idx_{$column} ({$column})"
                    ];
                }
            }

        } catch (\Exception $e) {
            Log::error("检查videos表索引失败: " . $e->getMessage());
        }

        return $recommendations;
    }

    /**
     * 检查users表索引
     */
    private function checkUserTableIndexes(): array
    {
        $recommendations = [];

        try {
            $indexes = $this->query("SHOW INDEX FROM users");
            $existingIndexes = array_column($indexes, 'Column_name');

            $recommendedIndexes = [
                'email' => '邮箱查询优化',
                'username' => '用户名查询优化',
                'status' => '状态过滤优化',
                'created_at' => '注册时间排序优化'
            ];

            foreach ($recommendedIndexes as $column => $description) {
                if (!in_array($column, $existingIndexes)) {
                    $recommendations[] = [
                        'table' => 'users',
                        'column' => $column,
                        'type' => 'index',
                        'description' => $description,
                        'sql' => "ALTER TABLE users ADD INDEX idx_{$column} ({$column})"
                    ];
                }
            }

        } catch (\Exception $e) {
            Log::error("检查users表索引失败: " . $e->getMessage());
        }

        return $recommendations;
    }

    /**
     * 检查其他表索引
     */
    private function checkOtherTableIndexes(): array
    {
        $recommendations = [];

        try {
            // 检查video_collections表
            $tables = ['video_collections', 'video_categories', 'comments'];

            foreach ($tables as $table) {
                $indexes = $this->query("SHOW INDEX FROM {$table}");
                $existingIndexes = array_column($indexes, 'Column_name');

                if ($table === 'video_collections' && !in_array('user_id', $existingIndexes)) {
                    $recommendations[] = [
                        'table' => $table,
                        'column' => 'user_id',
                        'type' => 'index',
                        'description' => '用户收藏查询优化',
                        'sql' => "ALTER TABLE {$table} ADD INDEX idx_user_id (user_id)"
                    ];
                }
            }

        } catch (\Exception $e) {
            Log::error("检查其他表索引失败: " . $e->getMessage());
        }

        return $recommendations;
    }

    /**
     * 获取优化建议
     */
    public function getOptimizationSuggestions(): array
    {
        $suggestions = [];

        try {
            // 检查是否存在推荐的索引
            $missingIndexes = $this->checkAndCreateIndexes();
            if (!empty($missingIndexes)) {
                $suggestions = array_merge($suggestions, $missingIndexes);
            }

            // 检查查询统计
            $stats = $this->getQueryStats();
            if ($stats['slow_query_rate'] > 5) {
                $suggestions[] = [
                    'type' => 'performance',
                    'priority' => 'high',
                    'description' => "慢查询率过高: {$stats['slow_query_rate']}%",
                    'action' => '建议检查慢查询日志并优化相关查询'
                ];
            }

            return $suggestions;

        } catch (\Exception $e) {
            Log::warning('获取数据库优化建议失败', [
                'error' => $e->getMessage()
            ]);
            return [];
        }
    }

    /**
     * 执行优化查询 (带性能监控)
     */
    public function executeOptimizedQuery(string $sql, array $params = [], array $options = [])
    {
        $startTime = microtime(true);
        $queryId = uniqid('query_');

        try {
            // 查询前处理
            $this->beforeQuery($sql, $params, $queryId);

            // 检查查询缓存
            if (($options['cache'] ?? false) && $this->optimizationConfig['enable_query_cache']) {
                $cacheKey = $this->generateQueryCacheKey($sql, $params);
                $cached = Cache::get($cacheKey);

                if ($cached !== null) {
                    $this->queryStats['cached_queries'] = ($this->queryStats['cached_queries'] ?? 0) + 1;
                    $this->logQueryPerformance($queryId, $sql, 0, 'cached');
                    return $cached;
                }
            }

            // 添加索引提示 (如果启用)
            if ($this->optimizationConfig['enable_index_hints']) {
                $sql = $this->addIndexHints($sql, $options['index_hints'] ?? []);
            }

            // 执行查询
            $result = Db::query($sql, $params);

            // 查询后处理
            $this->afterQuery($sql, $params, $queryId, $startTime, $result, $options);

            return $result;

        } catch (\Exception $e) {
            $this->handleQueryError($sql, $params, $queryId, $e);
            throw $e;
        }
    }

    /**
     * 查询前处理
     */
    private function beforeQuery(string $sql, array $params, string $queryId): void
    {
        // 记录查询开始
        Log::debug('查询开始', [
            'query_id' => $queryId,
            'sql' => $this->sanitizeSqlForLog($sql),
            'params_count' => count($params)
        ]);

        // 检查查询复杂度
        $this->analyzeQueryComplexity($sql, $queryId);
    }

    /**
     * 查询后处理
     */
    private function afterQuery(string $sql, array $params, string $queryId, float $startTime, $result, array $options): void
    {
        $endTime = microtime(true);
        $executionTime = ($endTime - $startTime) * 1000; // 转换为毫秒

        // 更新统计
        $this->updateQueryStats($sql, $executionTime);

        // 记录性能
        $this->logQueryPerformance($queryId, $sql, $executionTime, 'executed');

        // 慢查询分析
        if ($executionTime > ($this->slowQueryThreshold * 1000)) {
            $this->analyzeSlowQuery($sql, $params, $executionTime, $queryId);
        }

        // 缓存结果 (如果需要)
        if (($options['cache'] ?? false) && $this->optimizationConfig['enable_query_cache']) {
            $cacheKey = $this->generateQueryCacheKey($sql, $params);
            $cacheTTL = $options['cache_ttl'] ?? $this->optimizationConfig['query_cache_ttl'];
            Cache::set($cacheKey, $result, $cacheTTL);
        }

        // EXPLAIN分析 (如果启用且是慢查询)
        if ($this->optimizationConfig['enable_explain_analysis'] &&
            $executionTime > ($this->slowQueryThreshold * 1000)) {
            $this->performExplainAnalysis($sql, $params, $queryId);
        }
    }

    /**
     * 查询错误处理
     */
    private function handleQueryError(string $sql, array $params, string $queryId, \Exception $e): void
    {
        Log::error('查询执行失败', [
            'query_id' => $queryId,
            'sql' => $this->sanitizeSqlForLog($sql),
            'params_count' => count($params),
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);

        // 记录错误统计
        $this->queryStats['error_count'] = ($this->queryStats['error_count'] ?? 0) + 1;
    }

    /**
     * 生成查询缓存键
     */
    private function generateQueryCacheKey(string $sql, array $params): string
    {
        $normalizedSql = preg_replace('/\s+/', ' ', trim($sql));
        return 'query_cache:' . md5($normalizedSql . serialize($params));
    }

    /**
     * 添加索引提示
     */
    private function addIndexHints(string $sql, array $indexHints): string
    {
        if (empty($indexHints)) {
            return $sql;
        }

        foreach ($indexHints as $table => $indexes) {
            if (is_array($indexes)) {
                $hintStr = 'USE INDEX (' . implode(',', $indexes) . ')';
                $sql = str_replace(
                    "FROM {$table}",
                    "FROM {$table} {$hintStr}",
                    $sql
                );
            }
        }

        return $sql;
    }

    /**
     * 分析查询复杂度
     */
    private function analyzeQueryComplexity(string $sql, string $queryId): void
    {
        $complexity = 0;

        // 检查JOIN数量
        $joinCount = substr_count(strtoupper($sql), 'JOIN');
        $complexity += $joinCount * 2;

        // 检查子查询
        $subqueryCount = substr_count($sql, '(SELECT');
        $complexity += $subqueryCount * 3;

        // 检查ORDER BY
        if (stripos($sql, 'ORDER BY') !== false) {
            $complexity += 1;
        }

        // 检查GROUP BY
        if (stripos($sql, 'GROUP BY') !== false) {
            $complexity += 2;
        }

        // 记录复杂度
        if ($complexity > 10) {
            Log::warning('检测到复杂查询', [
                'query_id' => $queryId,
                'complexity_score' => $complexity,
                'sql' => $this->sanitizeSqlForLog($sql)
            ]);
        }
    }

    /**
     * 更新查询统计
     */
    private function updateQueryStats(string $sql, float $executionTime): void
    {
        $this->queryStats['total_queries'] = ($this->queryStats['total_queries'] ?? 0) + 1;
        $this->queryStats['total_time'] = ($this->queryStats['total_time'] ?? 0) + $executionTime;

        if ($executionTime > ($this->slowQueryThreshold * 1000)) {
            $this->queryStats['slow_queries']++;
        }

        // 按查询类型分类
        $queryType = $this->getQueryType($sql);
        $this->queryStats['by_type'][$queryType] = ($this->queryStats['by_type'][$queryType] ?? 0) + 1;
    }

    /**
     * 记录查询性能
     */
    private function logQueryPerformance(string $queryId, string $sql, float $executionTime, string $status): void
    {
        $logData = [
            'query_id' => $queryId,
            'execution_time_ms' => round($executionTime, 2),
            'status' => $status,
            'sql_preview' => $this->sanitizeSqlForLog($sql)
        ];

        if ($status === 'cached') {
            Log::debug('查询缓存命中', $logData);
        } elseif ($executionTime > ($this->slowQueryThreshold * 1000)) {
            Log::warning('慢查询检测', $logData);
        } else {
            Log::debug('查询执行完成', $logData);
        }
    }

    /**
     * 分析慢查询
     */
    private function analyzeSlowQuery(string $sql, array $params, float $executionTime, string $queryId): void
    {
        Log::warning('慢查询分析', [
            'query_id' => $queryId,
            'execution_time_ms' => round($executionTime, 2),
            'threshold_ms' => $this->slowQueryThreshold * 1000,
            'sql' => $this->sanitizeSqlForLog($sql),
            'params_count' => count($params)
        ]);

        // 生成优化建议
        $suggestions = $this->generateQueryOptimizationSuggestions($sql);
        if (!empty($suggestions)) {
            Log::info('查询优化建议', [
                'query_id' => $queryId,
                'suggestions' => $suggestions
            ]);
        }
    }

    /**
     * 执行EXPLAIN分析
     */
    private function performExplainAnalysis(string $sql, array $params, string $queryId): void
    {
        try {
            // 只对SELECT查询执行EXPLAIN
            if (stripos(trim($sql), 'SELECT') === 0) {
                $explainSql = 'EXPLAIN ' . $sql;
                $explainResult = Db::query($explainSql, $params);

                Log::info('EXPLAIN分析结果', [
                    'query_id' => $queryId,
                    'explain_result' => $explainResult
                ]);

                // 分析EXPLAIN结果
                $this->analyzeExplainResult($explainResult, $queryId);
            }
        } catch (\Exception $e) {
            Log::warning('EXPLAIN分析失败', [
                'query_id' => $queryId,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 分析EXPLAIN结果
     */
    private function analyzeExplainResult(array $explainResult, string $queryId): void
    {
        foreach ($explainResult as $row) {
            // 检查是否使用了索引
            if (empty($row['key']) || $row['key'] === 'NULL') {
                Log::warning('查询未使用索引', [
                    'query_id' => $queryId,
                    'table' => $row['table'],
                    'type' => $row['type']
                ]);
            }

            // 检查扫描行数
            if (isset($row['rows']) && $row['rows'] > 10000) {
                Log::warning('查询扫描行数过多', [
                    'query_id' => $queryId,
                    'table' => $row['table'],
                    'rows' => $row['rows']
                ]);
            }

            // 检查查询类型
            if (in_array($row['type'], ['ALL', 'index'])) {
                Log::warning('查询使用全表扫描', [
                    'query_id' => $queryId,
                    'table' => $row['table'],
                    'type' => $row['type']
                ]);
            }
        }
    }

    /**
     * 生成查询优化建议
     */
    private function generateQueryOptimizationSuggestions(string $sql): array
    {
        $suggestions = [];

        // 检查是否缺少WHERE条件
        if (stripos($sql, 'SELECT') === 0 && stripos($sql, 'WHERE') === false) {
            $suggestions[] = '建议添加WHERE条件以限制查询范围';
        }

        // 检查是否使用了SELECT *
        if (stripos($sql, 'SELECT *') !== false) {
            $suggestions[] = '建议指定具体字段而不是使用SELECT *';
        }

        // 检查是否有ORDER BY但没有LIMIT
        if (stripos($sql, 'ORDER BY') !== false && stripos($sql, 'LIMIT') === false) {
            $suggestions[] = '建议在ORDER BY查询中添加LIMIT以提高性能';
        }

        // 检查是否有多个JOIN
        $joinCount = substr_count(strtoupper($sql), 'JOIN');
        if ($joinCount > 3) {
            $suggestions[] = '建议减少JOIN数量或考虑查询重构';
        }

        return $suggestions;
    }

    /**
     * 获取查询类型
     */
    private function getQueryType(string $sql): string
    {
        $sql = trim(strtoupper($sql));

        if (strpos($sql, 'SELECT') === 0) return 'SELECT';
        if (strpos($sql, 'INSERT') === 0) return 'INSERT';
        if (strpos($sql, 'UPDATE') === 0) return 'UPDATE';
        if (strpos($sql, 'DELETE') === 0) return 'DELETE';
        if (strpos($sql, 'CREATE') === 0) return 'CREATE';
        if (strpos($sql, 'ALTER') === 0) return 'ALTER';
        if (strpos($sql, 'DROP') === 0) return 'DROP';

        return 'OTHER';
    }

    /**
     * 清理SQL用于日志记录
     */
    private function sanitizeSqlForLog(string $sql): string
    {
        // 移除多余的空白字符
        $sql = preg_replace('/\s+/', ' ', trim($sql));

        // 限制长度
        if (strlen($sql) > 200) {
            $sql = substr($sql, 0, 200) . '...';
        }

        return $sql;
    }

    /**
     * 获取查询性能报告
     */
    public function getPerformanceReport(): array
    {
        $stats = $this->getQueryStats();

        return [
            'summary' => [
                'total_queries' => $stats['total_queries'] ?? 0,
                'slow_queries' => $stats['slow_queries'] ?? 0,
                'cached_queries' => $stats['cached_queries'] ?? 0,
                'error_queries' => $stats['error_count'] ?? 0,
                'avg_execution_time' => $this->calculateAverageExecutionTime(),
                'slow_query_rate' => $this->calculateSlowQueryRate()
            ],
            'by_type' => $stats['by_type'] ?? [],
            'optimization_suggestions' => $this->getOptimizationSuggestions(),
            'index_suggestions' => $this->indexSuggestions,
            'generated_at' => date('Y-m-d H:i:s')
        ];
    }

    /**
     * 计算平均执行时间
     */
    private function calculateAverageExecutionTime(): float
    {
        $totalQueries = $this->queryStats['total_queries'] ?? 0;
        $totalTime = $this->queryStats['total_time'] ?? 0;

        return $totalQueries > 0 ? round($totalTime / $totalQueries, 2) : 0;
    }

    /**
     * 计算慢查询率
     */
    private function calculateSlowQueryRate(): float
    {
        $totalQueries = $this->queryStats['total_queries'] ?? 0;
        $slowQueries = $this->queryStats['slow_queries'] ?? 0;

        return $totalQueries > 0 ? round(($slowQueries / $totalQueries) * 100, 2) : 0;
    }
}
