/**
 * 🔧 管理后台配置文件
 *
 * 暂时恢复独立配置，避免TypeScript项目配置问题
 * 后续将通过其他方式实现配置统一
 */

// 环境类型
export type Environment = 'development' | 'production' | 'test'

// 服务配置接口
export interface ServiceConfig {
  api: string
  admin: string
  frontend: string
}

// 获取当前环境
export const getEnvironment = (): Environment => {
  return (import.meta.env.MODE as Environment) || 'development'
}

// 获取环境变量值，支持默认值
const getEnvVar = (key: string, defaultValue: string): string => {
  if (import.meta.env?.[key]) {
    return import.meta.env[key]
  }
  return defaultValue
}

// 不同环境的服务配置（支持环境变量）
const configs: Record<Environment, ServiceConfig> = {
  // 开发环境 - 支持环境变量配置
  development: {
    api: getEnvVar('VITE_API_BASE_URL', 'http://localhost:3000'),
    admin: getEnvVar('VITE_ADMIN_URL', 'http://localhost:3001'),
    frontend: getEnvVar('VITE_FRONTEND_URL', 'http://localhost:3002')
  },

  // 生产环境 - 支持环境变量配置
  production: {
    api: getEnvVar('VITE_API_BASE_URL', '/api'),
    admin: getEnvVar('VITE_ADMIN_URL', 'http://*************:3001'),
    frontend: getEnvVar('VITE_FRONTEND_URL', 'http://*************:3002')
  },

  // 测试环境
  test: {
    api: getEnvVar('VITE_API_BASE_URL', 'http://test-api.hpshishui.asia'),
    admin: getEnvVar('VITE_ADMIN_URL', 'http://test-admin.hpshishui.asia'),
    frontend: getEnvVar('VITE_FRONTEND_URL', 'http://test-shipin.hpshishui.asia')
  }
}

// 获取当前环境的配置
export const getConfig = (): ServiceConfig => {
  const env = getEnvironment()
  return configs[env]
}

// 导出具体的服务地址
export const config = getConfig()

// 环境检查函数
export const isDevelopment = (): boolean => getEnvironment() === 'development'
export const isProduction = (): boolean => getEnvironment() === 'production'
export const isTest = (): boolean => getEnvironment() === 'test'

// API配置
export const API_CONFIG = {
  baseURL: config.api,
  timeout: 15000, // 增加到15秒，给采集源测试更多时间
  headers: {
    'Content-Type': 'application/json'
  }
}

// 🛣️ API路径配置 - 管理员专用（基于共享设计，避免导入问题）
export const API_PATHS = {
  // 🔐 管理员认证
  ADMIN: {
    LOGIN: '/api/admin/login',          // POST - 管理员登录
    PROFILE: '/api/admin/profile',      // GET - 获取管理员信息
    LOGOUT: '/api/admin/logout'         // POST - 管理员登出
  },

  // 📊 仪表盘
  DASHBOARD: {
    DATA: '/api/admin/dashboard',       // GET - 仪表盘数据
    STATS: '/api/admin/dashboard/stats' // GET - 统计数据
  },

  // 👥 用户管理 - RESTful CRUD
  USERS: {
    LIST: '/api/admin/users',           // GET - 用户列表
    CREATE: '/api/admin/users',         // POST - 创建用户
    DETAIL: '/api/admin/users',         // GET - 用户详情 /:id
    UPDATE: '/api/admin/users',         // PUT - 更新用户 /:id
    DELETE: '/api/admin/users'          // DELETE - 删除用户 /:id
  },

  // 🎬 视频管理 - RESTful CRUD
  VIDEOS: {
    LIST: '/api/admin/videos',          // GET - 视频列表
    CREATE: '/api/admin/videos',        // POST - 创建视频
    DETAIL: '/api/admin/videos',        // GET - 视频详情 /:id
    UPDATE: '/api/admin/videos',        // PUT - 更新视频 /:id
    DELETE: '/api/admin/videos',        // DELETE - 删除视频 /:id
    AUDIT: '/api/admin/videos'          // PUT - 审核视频 /:id/audit
  },

  // 📁 分类管理 - RESTful CRUD
  CATEGORIES: {
    LIST: '/api/admin/categories',      // GET - 分类列表
    CREATE: '/api/admin/categories',    // POST - 创建分类
    DETAIL: '/api/admin/categories',    // GET - 分类详情 /:id
    UPDATE: '/api/admin/categories',    // PUT - 更新分类 /:id
    DELETE: '/api/admin/categories'     // DELETE - 删除分类 /:id
  },

  // 📊 统计数据
  STATISTICS: {
    OVERVIEW: '/admin/statistics/overview',        // GET - 总体统计
    USER_GROWTH: '/admin/statistics/user-growth',  // GET - 用户增长趋势
    VIDEO_GROWTH: '/admin/statistics/video-growth', // GET - 视频发布趋势
    TOP_VIDEOS: '/admin/statistics/top-videos',    // GET - 热门视频排行
    CATEGORY_STATS: '/admin/statistics/category-stats', // GET - 分类统计
    ACTIVE_USERS: '/admin/statistics/active-users', // GET - 活跃用户排行
    REALTIME: '/admin/statistics/realtime'         // GET - 实时数据
  },

  // 💬 评论管理
  COMMENTS: {
    LIST: '/api/admin/admin-comments',     // GET - 获取所有评论
    UPDATE: '/api/admin/admin-comments',   // PUT - 审核评论 /:id
    DELETE: '/api/admin/admin-comments'    // DELETE - 删除评论 /:id
  },

  // ⚙️ 系统设置
  SETTINGS: {
    GET: '/api/admin/settings',         // GET - 获取系统设置
    UPDATE: '/api/admin/settings'       // PUT - 更新系统设置
  },

  // 📤 文件上传
  UPLOAD: {
    IMAGE: '/api/upload/image',         // POST - 上传图片
    VIDEO: '/api/upload/video',         // POST - 上传视频
    AVATAR: '/api/upload/avatar'        // POST - 上传头像
  },

  // 🎬 视频处理管理
  VIDEO_PROCESSING: {
    STATUS: '/api/admin/video-processing/status',           // GET - 获取处理状态
    START_WORKER: '/api/admin/video-processing/start-worker',   // POST - 启动工作进程
    STOP_WORKER: '/api/admin/video-processing/stop-worker',     // POST - 停止工作进程
    RESTART_WORKER: '/api/admin/video-processing/restart-worker', // POST - 重启工作进程
    BATCH_REPROCESS: '/api/admin/video-processing/batch-reprocess', // POST - 批量重新处理
    CLEAR_QUEUE: '/api/admin/video-processing/clear-queue'      // POST - 清理队列
  }
}

// 🔗 构建完整URL的辅助函数
export const buildApiUrl = (basePath: string, id?: string | number, action?: string): string => {
  let url = basePath

  if (id) {
    url += `/${id}`
  }

  if (action) {
    url += `/${action}`
  }

  return url
}

// 🛣️ 管理后台路由配置
export const ROUTES = {
  LOGIN: '/login',
  DASHBOARD: '/dashboard',
  USERS: '/users',
  VIDEOS: '/videos',
  CATEGORIES: '/categories',
  SETTINGS: '/settings',
  PROFILE: '/profile'
}

// 🔐 管理后台专用存储配置
export const STORAGE_CONFIG = {
  // localStorage键名
  TOKEN_KEY: 'admin_token',
  USER_KEY: 'admin_user',
  SETTINGS_KEY: 'admin_settings',

  // sessionStorage键名
  TEMP_DATA_KEY: 'temp_data'
}

// 📤 上传配置（管理后台专用）
export const UPLOAD_CONFIG = {
  // 文件大小限制 (MB)
  MAX_FILE_SIZE: {
    AVATAR: 2,
    VIDEO_COVER: 5,
    SHORT_VIDEO: 100,
    LONG_VIDEO: 500,
    DOCUMENT: 10  // 管理后台支持文档上传
  },

  // 支持的文件类型
  ALLOWED_TYPES: {
    IMAGE: ['jpg', 'jpeg', 'png', 'gif', 'webp'],
    VIDEO: ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'],
    DOCUMENT: ['pdf', 'doc', 'docx', 'txt', 'xlsx', 'pptx']  // 管理后台支持文档
  },

  // 上传分片大小 (MB)
  CHUNK_SIZE: 5
}

// 📄 分页配置（管理后台专用）
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 20,
  SHORT_VIDEO_PAGE_SIZE: 10,
  COMMENT_PAGE_SIZE: 15,
  MAX_PAGE_SIZE: 100,  // 管理后台支持更大的分页
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100]
}

// 🎨 UI配置
export const UI_CONFIG = {
  // 主题配置
  THEME: {
    PRIMARY_COLOR: '#1890ff',
    SUCCESS_COLOR: '#52c41a',
    WARNING_COLOR: '#faad14',
    ERROR_COLOR: '#f5222d'
  },

  // 动画配置
  ANIMATION: {
    DURATION: 300,
    EASING: 'ease-in-out'
  },

  // 布局配置
  LAYOUT: {
    HEADER_HEIGHT: 64,
    SIDEBAR_WIDTH: 200,
    SIDEBAR_COLLAPSED_WIDTH: 80
  }
}

// 🔒 安全配置
export const SECURITY_CONFIG = {
  // Token配置
  TOKEN: {
    STORAGE_KEY: 'admin_token',
    REFRESH_THRESHOLD: 5 * 60 * 1000, // 5分钟前刷新
    MAX_RETRY_COUNT: 3
  },

  // 密码规则
  PASSWORD: {
    MIN_LENGTH: 6,
    MAX_LENGTH: 20,
    REQUIRE_UPPERCASE: false,
    REQUIRE_LOWERCASE: false,
    REQUIRE_NUMBERS: true,
    REQUIRE_SYMBOLS: false
  }
}

// 🔧 导出默认配置（兼容现有代码）
export default {
  config,
  API_CONFIG,
  API_PATHS,
  ROUTES,
  STORAGE_CONFIG,
  UPLOAD_CONFIG,
  PAGINATION_CONFIG,
  UI_CONFIG,
  SECURITY_CONFIG,
  getEnvironment,
  getConfig,
  buildApiUrl,
  isDevelopment,
  isProduction
}

// 🐛 调试信息 - 在所有环境下都显示
console.log('🔧 管理后台配置加载完成:', {
  environment: getEnvironment(),
  config: config,
  apiConfig: API_CONFIG,
  envVars: {
    VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL,
    MODE: import.meta.env.MODE
  }
})
