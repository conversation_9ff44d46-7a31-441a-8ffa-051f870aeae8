import { request } from './request'
import type { 
  ApiResponse, 
  Video, 
  VideoListParams, 
  VideoSearchParams,
  VideoUploadParams,
  VideoStats 
} from '@/types/video'

/**
 * 企业级视频API接口 - [v20250719]
 * 
 * 提供完整的视频相关API调用功能
 * 支持列表获取、搜索、上传、统计等企业级功能
 * 
 * <AUTHOR> Video Platform Team
 * @version 2.0
 */

// API路径常量
const API_PATHS = {
  VIDEOS: {
    LIST: '/api/videos',
    DETAIL: '/api/videos/:id',
    SEARCH: '/api/videos/search',
    UPLOAD: '/api/v1/upload/video',
    UPDATE: '/api/videos/:id',
    DELETE: '/api/videos/:id',
    LIKE: '/api/videos/:id/like',
    UNLIKE: '/api/videos/:id/unlike',
    FAVORITE: '/api/videos/:id/favorite',
    UNFAVORITE: '/api/videos/:id/unfavorite',
    REPORT_PLAY: '/api/videos/:id/play',
    STATS: '/api/videos/:id/stats',
    TRENDING: '/api/videos/trending',
    RECOMMENDED: '/api/videos/recommended',
  },
  CATEGORIES: {
    LIST: '/api/categories',
    VIDEOS: '/api/categories/:id/videos',
  },
  TAGS: {
    LIST: '/api/tags',
    POPULAR: '/api/tags/popular',
  }
} as const

/**
 * 视频API类
 */
export class VideoApi {
  /**
   * 获取视频列表
   */
  async getList(params: VideoListParams = {}): Promise<ApiResponse<{
    videos: Video[]
    total: number
    page: number
    pageSize: number
    totalPages: number
  }>> {
    return request.get(API_PATHS.VIDEOS.LIST, { params })
  }

  /**
   * 获取视频详情
   */
  async getDetail(id: number): Promise<ApiResponse<Video>> {
    const url = API_PATHS.VIDEOS.DETAIL.replace(':id', id.toString())
    return request.get(url)
  }

  /**
   * 搜索视频
   */
  async search(params: VideoSearchParams): Promise<ApiResponse<{
    videos: Video[]
    total: number
    suggestions?: string[]
  }>> {
    return request.get(API_PATHS.VIDEOS.SEARCH, { params })
  }

  /**
   * 获取推荐视频
   */
  async getRecommended(params: {
    page?: number
    pageSize?: number
    userId?: number
  } = {}): Promise<ApiResponse<{
    videos: Video[]
    total: number
  }>> {
    return request.get(API_PATHS.VIDEOS.RECOMMENDED, { params })
  }

  /**
   * 获取热门视频
   */
  async getTrending(params: {
    page?: number
    pageSize?: number
    timeRange?: 'day' | 'week' | 'month'
  } = {}): Promise<ApiResponse<{
    videos: Video[]
    total: number
  }>> {
    return request.get(API_PATHS.VIDEOS.TRENDING, { params })
  }

  /**
   * 上传视频
   */
  async upload(params: VideoUploadParams & {
    onProgress?: (progress: number) => void
  }): Promise<ApiResponse<{
    videoId: number
    uploadUrl?: string
    uploadToken?: string
  }>> {
    const formData = new FormData()

    // 添加文件
    if (params.file) {
      formData.append('video', params.file) // 使用'video'字段名
    }

    // 添加其他参数
    Object.entries(params).forEach(([key, value]) => {
      if (key !== 'file' && key !== 'onProgress' && value !== undefined) {
        if (key === 'categoryId') {
          formData.append('category_id', String(value))
        } else if (key === 'videoType') {
          formData.append('video_type', String(value))
        } else {
          formData.append(key, typeof value === 'object' ? JSON.stringify(value) : String(value))
        }
      }
    })

    return request.post(API_PATHS.VIDEOS.UPLOAD, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 300000, // 5分钟超时
      onUploadProgress: (progressEvent) => {
        if (params.onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          params.onProgress(progress)
        }
      }
    })
  }

  /**
   * 更新视频信息
   */
  async update(id: number, data: Partial<Video>): Promise<ApiResponse<Video>> {
    const url = API_PATHS.VIDEOS.UPDATE.replace(':id', id.toString())
    return request.put(url, data)
  }

  /**
   * 删除视频
   */
  async delete(id: number): Promise<ApiResponse<null>> {
    const url = API_PATHS.VIDEOS.DELETE.replace(':id', id.toString())
    return request.delete(url)
  }

  /**
   * 点赞视频
   */
  async like(id: number): Promise<ApiResponse<{
    liked: boolean
    likeCount: number
  }>> {
    const url = API_PATHS.VIDEOS.LIKE.replace(':id', id.toString())
    return request.post(url)
  }

  /**
   * 取消点赞
   */
  async unlike(id: number): Promise<ApiResponse<{
    liked: boolean
    likeCount: number
  }>> {
    const url = API_PATHS.VIDEOS.UNLIKE.replace(':id', id.toString())
    return request.delete(url)
  }

  /**
   * 收藏视频
   */
  async favorite(id: number): Promise<ApiResponse<{
    favorited: boolean
    favoriteCount: number
  }>> {
    const url = API_PATHS.VIDEOS.FAVORITE.replace(':id', id.toString())
    return request.post(url)
  }

  /**
   * 取消收藏
   */
  async unfavorite(id: number): Promise<ApiResponse<{
    favorited: boolean
    favoriteCount: number
  }>> {
    const url = API_PATHS.VIDEOS.UNFAVORITE.replace(':id', id.toString())
    return request.delete(url)
  }

  /**
   * 收藏视频 (别名)
   */
  async collect(id: number) {
    return this.favorite(id)
  }

  /**
   * 取消收藏 (别名)
   */
  async uncollect(id: number) {
    return this.unfavorite(id)
  }

  /**
   * 上报播放事件
   */
  async reportPlay(id: number, data: {
    playTime?: number
    duration?: number
    quality?: string
    device?: string
  } = {}): Promise<ApiResponse<null>> {
    const url = API_PATHS.VIDEOS.REPORT_PLAY.replace(':id', id.toString())
    return request.post(url, data)
  }

  /**
   * 获取视频统计信息
   */
  async getStats(id: number): Promise<ApiResponse<VideoStats>> {
    const url = API_PATHS.VIDEOS.STATS.replace(':id', id.toString())
    return request.get(url)
  }

  /**
   * 获取分类列表
   */
  async getCategories(): Promise<ApiResponse<Array<{
    id: number
    name: string
    description?: string
    videoCount: number
    icon?: string
  }>>> {
    return request.get(API_PATHS.CATEGORIES.LIST)
  }

  /**
   * 获取分类下的视频
   */
  async getCategoryVideos(categoryId: number, params: {
    page?: number
    pageSize?: number
    sortBy?: string
    sortOrder?: 'asc' | 'desc'
  } = {}): Promise<ApiResponse<{
    videos: Video[]
    total: number
    category: {
      id: number
      name: string
      description?: string
    }
  }>> {
    const url = API_PATHS.CATEGORIES.VIDEOS.replace(':id', categoryId.toString())
    return request.get(url, { params })
  }

  /**
   * 获取标签列表
   */
  async getTags(): Promise<ApiResponse<Array<{
    id: number
    name: string
    videoCount: number
    color?: string
  }>>> {
    return request.get(API_PATHS.TAGS.LIST)
  }

  /**
   * 获取热门标签
   */
  async getPopularTags(limit = 20): Promise<ApiResponse<Array<{
    id: number
    name: string
    videoCount: number
    trendingScore: number
  }>>> {
    return request.get(API_PATHS.TAGS.POPULAR, { 
      params: { limit } 
    })
  }

  /**
   * 批量操作视频
   */
  async batchOperation(operation: 'delete' | 'favorite' | 'unfavorite', videoIds: number[]): Promise<ApiResponse<{
    success: number[]
    failed: number[]
    errors: Record<number, string>
  }>> {
    return request.post('/api/v1/videos/batch', {
      operation,
      videoIds
    })
  }

  /**
   * 获取用户的视频
   */
  async getUserVideos(userId: number, params: {
    page?: number
    pageSize?: number
    status?: string
  } = {}): Promise<ApiResponse<{
    videos: Video[]
    total: number
  }>> {
    return request.get(`/api/v1/users/${userId}/videos`, { params })
  }

  /**
   * 获取用户点赞的视频
   */
  async getUserLikedVideos(params: {
    page?: number
    pageSize?: number
  } = {}): Promise<ApiResponse<{
    videos: Video[]
    total: number
  }>> {
    return request.get('/api/v1/users/me/liked-videos', { params })
  }

  /**
   * 获取用户收藏的视频
   */
  async getUserFavoriteVideos(params: {
    page?: number
    pageSize?: number
  } = {}): Promise<ApiResponse<{
    videos: Video[]
    total: number
  }>> {
    return request.get('/api/v1/users/me/favorite-videos', { params })
  }

  /**
   * 获取观看历史
   */
  async getWatchHistory(params: {
    page?: number
    pageSize?: number
  } = {}): Promise<ApiResponse<{
    videos: Video[]
    total: number
  }>> {
    return request.get('/api/v1/users/me/watch-history', { params })
  }

  /**
   * 清空观看历史
   */
  async clearWatchHistory(): Promise<ApiResponse<null>> {
    return request.delete('/api/v1/users/me/watch-history')
  }

  /**
   * 举报视频
   */
  async reportVideo(id: number, data: {
    reason: string
    description?: string
    category: string
  }): Promise<ApiResponse<null>> {
    return request.post(`/api/v1/videos/${id}/report`, data)
  }

  /**
   * 获取视频评论
   */
  async getComments(id: number, params: {
    page?: number
    pageSize?: number
    sortBy?: 'created_at' | 'likes'
    sortOrder?: 'asc' | 'desc'
  } = {}): Promise<ApiResponse<{
    comments: Array<{
      id: number
      content: string
      user: {
        id: number
        username: string
        avatar?: string
      }
      likes: number
      replies: number
      createdAt: string
      isLiked: boolean
    }>
    total: number
  }>> {
    return request.get(`/api/v1/videos/${id}/comments`, { params })
  }

  /**
   * 添加评论
   */
  async addComment(id: number, data: {
    content: string
    parentId?: number
  }): Promise<ApiResponse<{
    id: number
    content: string
    createdAt: string
  }>> {
    return request.post(`/api/v1/videos/${id}/comments`, data)
  }

  /**
   * 删除评论
   */
  async deleteComment(videoId: number, commentId: number): Promise<ApiResponse<null>> {
    return request.delete(`/api/v1/videos/${videoId}/comments/${commentId}`)
  }
}

// 创建API实例
const videoApiInstance = new VideoApi()

// 扩展API实例，添加兼容性方法
export const videoApi = Object.assign(videoApiInstance, {
  // 获取视频列表 - 兼容新组件的参数格式
  async getVideoList(params: any = {}) {
    try {
      // 直接调用后端API - 使用v1版本路径
      const response = await request.get('/api/v1/videos', { params })

      // 适配数据格式 - 响应拦截器已经处理了格式转换
      if (response.success && response.data) {
        return {
          code: 200,
          message: response.message || 'success',
          data: {
            data: response.data.videos || [],
            has_more: response.data.page < response.data.pages,
            total: response.data.total || 0
          }
        }
      }

      return {
        code: 500,
        message: response.message || '获取视频列表失败',
        data: {
          data: [],
          has_more: false,
          total: 0
        }
      }
    } catch (error) {
      console.error('获取视频列表失败:', error)
      return {
        code: 500,
        message: '获取视频列表失败',
        data: {
          data: [],
          has_more: false,
          total: 0
        }
      }
    }
  },

  // 获取视频详情 - 兼容新组件的调用方式
  async getVideoDetail(id: string | number) {
    try {
      // 使用单个视频详情接口，不是列表接口
      const response = await request.get(`/api/video/${id}`)
      return response
    } catch (error) {
      console.error('获取视频详情失败:', error)
      return {
        success: false,
        message: '获取视频详情失败',
        data: null
      }
    }
  },

  // 搜索视频 - 兼容新组件的调用方式
  async searchVideos(params: any = {}) {
    try {
      const searchParams = {
        keyword: params.keyword,
        page: params.page || 1,
        limit: params.limit || 20
      }

      const response = await request.get('/api/search/videos', { params: searchParams })

      // 适配数据格式
      if (response.code === 200 && response.data) {
        return {
          code: 200,
          message: response.message || 'success',
          data: {
            data: response.data.videos || [],
            has_more: response.data.page < response.data.pages,
            total: response.data.total || 0
          }
        }
      }

      return response
    } catch (error) {
      console.error('搜索视频失败:', error)
      return {
        code: 500,
        message: '搜索视频失败',
        data: {
          data: [],
          has_more: false,
          total: 0
        }
      }
    }
  },

  // 获取推荐视频
  async getRecommended(params: {
    page?: number
    pageSize?: number
    userId?: number
    videoType?: string
  } = {}) {
    try {
      const requestParams = {
        page: params.page || 1,
        limit: params.pageSize || 20,
        video_type: params.videoType || 'long' // 修正参数名，默认获取长视频
      }

      const response = await request.get('/api/videos/recommend', { params: requestParams })

      console.log('🔧 getRecommended API原始响应:', response)

      // request.ts已经处理了响应格式，直接使用
      if (response.success && response.data) {
        return {
          success: true,
          message: response.message || 'success',
          data: {
            videos: Array.isArray(response.data) ? response.data : [],
            total: Array.isArray(response.data) ? response.data.length : 0
          }
        }
      }

      return {
        success: false,
        message: response.message || '获取推荐视频失败',
        data: {
          videos: [],
          total: 0
        }
      }
    } catch (error) {
      console.error('获取推荐视频失败:', error)
      return {
        success: false,
        message: '获取推荐视频失败',
        data: {
          videos: [],
          total: 0
        }
      }
    }
  }
})

// 简化的API方法，用于兼容现有代码
export const getCategories = () => {
  return request.get('/api/v1/categories')
}

export const getUserVideos = (params: {
  page?: number
  limit?: number
  status?: string
  audit_status?: string
}) => {
  // 需要根据当前用户获取视频，这里使用通用的videos接口，后端会根据用户认证信息筛选
  return request.get('/api/v1/videos', {
    params: {
      ...params,
      user_only: true // 标识只获取当前用户的视频
    }
  })
}

export const getVideoProcessingStatus = (videoId: number) => {
  return request.get(`/api/videos/${videoId}/processing-status`)
}

export const getBatchVideoProcessingStatus = (videoIds: number[]) => {
  return request.get('/api/videos/processing-status/batch', {
    params: { video_ids: videoIds.join(',') }
  })
}

export default videoApi
