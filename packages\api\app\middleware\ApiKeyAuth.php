<?php
declare(strict_types=1);

namespace app\middleware;

use Closure;
use think\Request;
use think\Response;
use think\facade\Cache;
use think\facade\Log;

/**
 * API密钥认证中间件
 * 替代CORS，提供更安全的认证方式
 */
class ApiKeyAuth
{
    /**
     * API密钥配置 - 从环境变量读取
     */
    private array $apiKeys;

    /**
     * 构造函数 - 初始化API密钥配置
     */
    public function __construct()
    {
        $this->apiKeys = [
            // 管理后台密钥
            'admin_frontend' => [
                'key' => env('API_KEY_ADMIN', 'ShiPinAdmin2024ProductionKey32Bytes!@#$%^&*()_+'),
                'name' => '管理后台',
                'permissions' => ['*'], // 临时全权限测试
                'rate_limit' => 1000,
                'allowed_ips' => [],
                'expires_at' => null
            ],

            // 用户前端密钥
            'user_frontend' => [
                'key' => env('API_KEY_USER', 'ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+'),
                'name' => '用户前端',
                'permissions' => ['*'], // 临时给予全权限以便测试
                'rate_limit' => 500,
                'allowed_ips' => [],
                'expires_at' => null
            ],

            // 移动端密钥
            'mobile_app' => [
                'key' => env('API_KEY_MOBILE', 'zhengshiban_mobile_dev_2024_secure_key_v1'),
                'name' => '移动端应用',
                'permissions' => ['video.list', 'video.detail', 'user.*'],
                'rate_limit' => 300,
                'allowed_ips' => [],
                'expires_at' => null
            ],

            // 采集系统密钥
            'collect_system' => [
                'key' => env('API_KEY_COLLECT', 'zhengshiban_collect_dev_2024_secure_key_v1'),
                'name' => '采集系统',
                'permissions' => ['collect.*', 'video.create', 'video.update'],
                'rate_limit' => 2000,
                'allowed_ips' => ['127.0.0.1', '************'],
                'expires_at' => null
            ],

            // 第三方API密钥
            'third_party' => [
                'key' => env('API_KEY_THIRD_PARTY', 'zhengshiban_api_dev_2024_secure_key_v1'),
                'name' => '第三方接口',
                'permissions' => ['video.list', 'video.detail'],
                'rate_limit' => 100,
                'allowed_ips' => [],
                'expires_at' => '2025-12-31 23:59:59'
            ]
        ];
    }

    /**
     * 不需要API密钥的公开路由
     */
    private array $publicRoutes = [
        'health',
        'api/v1/health',    // API健康检查
        'simple-test',
        'test-cors',
        'test-db',          // 数据库测试接口
        'test-video-status', // 视频状态测试接口
        'hls-test',         // HLS测试接口
        'api/public/*',
        'simple',           // 简单测试路由
        'test-route',       // 测试路由
        'test/register',    // 测试注册接口
        'test/login',       // 测试登录接口
        'environment/*',    // 环境检查接口
        'storage/*',        // 静态文件
        'uploads/*',        // 上传文件
        'hls/*'             // HLS视频流
    ];

    /**
     * 处理请求
     */
    public function handle(Request $request, Closure $next): Response
    {
        // 检查是否为公开路由
        if ($this->isPublicRoute($request->pathinfo())) {
            return $next($request);
        }

        // 获取API密钥
        $apiKey = $this->extractApiKey($request);
        
        if (!$apiKey) {
            return $this->unauthorizedResponse('API密钥缺失');
        }

        // 验证API密钥
        $keyConfig = $this->validateApiKey($apiKey);
        
        if (!$keyConfig) {
            return $this->unauthorizedResponse('无效的API密钥');
        }

        // 检查IP限制
        if (!$this->checkIpRestriction($request, $keyConfig)) {
            return $this->forbiddenResponse('IP地址不被允许');
        }

        // 检查过期时间
        if (!$this->checkExpiration($keyConfig)) {
            return $this->unauthorizedResponse('API密钥已过期');
        }

        // 检查权限
        if (!$this->checkPermissions($request, $keyConfig)) {
            return $this->forbiddenResponse('权限不足');
        }

        // 检查速率限制 - 暂时禁用以排查问题
        // if (!$this->checkRateLimit($request, $keyConfig)) {
        //     return $this->rateLimitResponse('请求频率超限');
        // }

        // 记录API使用日志
        $this->logApiUsage($request, $keyConfig);

        // 将密钥信息添加到请求中，供后续使用
        $request->apiKeyConfig = $keyConfig;

        return $next($request);
    }

    /**
     * 检查是否为公开路由
     */
    private function isPublicRoute(string $path): bool
    {
        foreach ($this->publicRoutes as $route) {
            if ($route === $path || (str_ends_with($route, '*') && str_starts_with($path, rtrim($route, '*')))) {
                return true;
            }
        }

        return false;
    }

    /**
     * 从请求中提取API密钥
     */
    private function extractApiKey(Request $request): ?string
    {
        // 优先从Header中获取
        $apiKey = $request->header('X-API-Key') ?: $request->header('Authorization');

        if ($apiKey && str_starts_with($apiKey, 'Bearer ')) {
            $apiKey = substr($apiKey, 7);
        }

        // 其次从查询参数获取（不推荐，但兼容某些场景）
        if (!$apiKey) {
            $apiKey = $request->param('api_key');
        }

        // 调试日志
        Log::info('API Key Debug', [
            'path' => $request->pathinfo(),
            'method' => $request->method(),
            'x_api_key_header' => $request->header('X-API-Key'),
            'authorization_header' => $request->header('Authorization'),
            'api_key_param' => $request->param('api_key'),
            'extracted_key' => $apiKey ? substr($apiKey, 0, 10) . '...' : 'null',
            'all_headers' => $request->header()
        ]);

        return $apiKey;
    }

    /**
     * 验证API密钥
     */
    private function validateApiKey(string $apiKey): ?array
    {
        foreach ($this->apiKeys as $keyId => $config) {
            if ($config['key'] === $apiKey) {
                $config['key_id'] = $keyId;
                return $config;
            }
        }
        return null;
    }

    /**
     * 检查IP限制
     */
    private function checkIpRestriction(Request $request, array $keyConfig): bool
    {
        if (empty($keyConfig['allowed_ips'])) {
            return true; // 不限制IP
        }
        
        $clientIp = $request->ip();
        return in_array($clientIp, $keyConfig['allowed_ips']);
    }

    /**
     * 检查过期时间
     */
    private function checkExpiration(array $keyConfig): bool
    {
        if (!$keyConfig['expires_at']) {
            return true; // 永不过期
        }
        
        return strtotime($keyConfig['expires_at']) > time();
    }

    /**
     * 检查权限
     */
    private function checkPermissions(Request $request, array $keyConfig): bool
    {
        $route = $request->pathinfo();
        $permissions = $keyConfig['permissions'];

        // 调试日志
        Log::info('Permission Check', [
            'route' => $route,
            'permissions' => $permissions,
            'key_name' => $keyConfig['name']
        ]);

        foreach ($permissions as $permission) {
            // 全权限
            if ($permission === '*') {
                return true;
            }

            // 精确匹配
            if ($route === $permission) {
                return true;
            }

            // 通配符匹配
            if (str_ends_with($permission, '*')) {
                $prefix = rtrim($permission, '*');
                if (str_starts_with($route, $prefix)) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 检查速率限制
     */
    private function checkRateLimit(Request $request, array $keyConfig): bool
    {
        $keyId = $keyConfig['key_id'];
        $limit = $keyConfig['rate_limit'];
        $cacheKey = "api_rate_limit:{$keyId}:" . date('YmdH');
        
        $currentCount = Cache::get($cacheKey, 0);
        
        if ($currentCount >= $limit) {
            return false;
        }
        
        Cache::set($cacheKey, $currentCount + 1, 3600); // 1小时过期
        return true;
    }

    /**
     * 记录API使用日志
     */
    private function logApiUsage(Request $request, array $keyConfig): void
    {
        Log::info('API Key Usage', [
            'key_id' => $keyConfig['key_id'],
            'key_name' => $keyConfig['name'],
            'ip' => $request->ip(),
            'method' => $request->method(),
            'path' => $request->pathinfo(),
            'user_agent' => $request->header('User-Agent'),
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * 返回未授权响应
     */
    private function unauthorizedResponse(string $message): Response
    {
        return json([
            'success' => false,
            'code' => 401,
            'message' => $message,
            'data' => null
        ], 401);
    }

    /**
     * 返回禁止访问响应
     */
    private function forbiddenResponse(string $message): Response
    {
        return json([
            'success' => false,
            'code' => 403,
            'message' => $message,
            'data' => null
        ], 403);
    }

    /**
     * 返回速率限制响应
     */
    private function rateLimitResponse(string $message): Response
    {
        return json([
            'success' => false,
            'code' => 429,
            'message' => $message,
            'data' => null
        ], 429)->header('Retry-After', '3600');
    }
}
