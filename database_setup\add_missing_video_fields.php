<?php
/**
 * 添加videos表缺失的字段
 * 修复分片上传完成接口500错误
 */

$host = 'mysql';
$username = 'root';
$password = 'root123456';
$database = 'zhengshiban_dev';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]);
    
    echo "数据库连接成功\n";
    
    // 检查并添加缺失的字段
    $fieldsToAdd = [
        'source_type' => "VARCHAR(50) DEFAULT 'upload' COMMENT '视频来源类型'",
        'file_size' => "BIGINT DEFAULT 0 COMMENT '文件大小（字节）'",
        'width' => "INT DEFAULT 0 COMMENT '视频宽度'",
        'height' => "INT DEFAULT 0 COMMENT '视频高度'",
        'format' => "VARCHAR(20) DEFAULT NULL COMMENT '视频格式'",
        'share_count' => "INT DEFAULT 0 COMMENT '分享数'",
        'collect_count' => "INT DEFAULT 0 COMMENT '收藏数'",
        'completion_rate' => "DECIMAL(5,2) DEFAULT 0.00 COMMENT '完播率'",
        'engagement_score' => "DECIMAL(8,2) DEFAULT 0.00 COMMENT '互动评分'",
        'is_featured' => "TINYINT(1) DEFAULT 0 COMMENT '是否精选'",
        'is_private' => "TINYINT(1) DEFAULT 0 COMMENT '是否私密'",
        'is_free' => "TINYINT(1) DEFAULT 1 COMMENT '是否免费'",
        'points_price' => "INT DEFAULT 0 COMMENT '积分价格'"
    ];
    
    foreach ($fieldsToAdd as $fieldName => $fieldDefinition) {
        // 检查字段是否存在
        $checkSql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                     WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'videos' AND COLUMN_NAME = ?";
        $stmt = $pdo->prepare($checkSql);
        $stmt->execute([$database, $fieldName]);
        $exists = $stmt->fetchColumn() > 0;
        
        if (!$exists) {
            echo "添加字段: {$fieldName}...\n";
            $alterSql = "ALTER TABLE `videos` ADD COLUMN `{$fieldName}` {$fieldDefinition}";
            $pdo->exec($alterSql);
            echo "✅ 字段 {$fieldName} 添加成功\n";
        } else {
            echo "⚠️ 字段 {$fieldName} 已存在，跳过\n";
        }
    }
    
    // 检查并添加索引
    $indexesToAdd = [
        'idx_source_type' => 'source_type',
        'idx_file_size' => 'file_size',
        'idx_is_featured' => 'is_featured',
        'idx_is_private' => 'is_private',
        'idx_is_free' => 'is_free',
        'idx_completion_rate' => 'completion_rate',
        'idx_engagement_score' => 'engagement_score'
    ];
    
    foreach ($indexesToAdd as $indexName => $columnName) {
        // 检查索引是否存在
        $checkIndexSql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
                          WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'videos' AND INDEX_NAME = ?";
        $stmt = $pdo->prepare($checkIndexSql);
        $stmt->execute([$database, $indexName]);
        $indexExists = $stmt->fetchColumn() > 0;
        
        if (!$indexExists) {
            echo "添加索引: {$indexName}...\n";
            $createIndexSql = "ALTER TABLE `videos` ADD INDEX `{$indexName}` (`{$columnName}`)";
            $pdo->exec($createIndexSql);
            echo "✅ 索引 {$indexName} 添加成功\n";
        } else {
            echo "⚠️ 索引 {$indexName} 已存在，跳过\n";
        }
    }
    
    echo "\n✅ 所有缺失字段和索引添加完成\n";
    
    // 显示更新后的表结构
    echo "\n📋 更新后的videos表结构:\n";
    $result = $pdo->query("DESCRIBE videos");
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo sprintf("%-20s %-30s %-10s %-10s %-15s %s\n", 
            $row['Field'], 
            $row['Type'], 
            $row['Null'], 
            $row['Key'], 
            $row['Default'], 
            $row['Extra']
        );
    }
    
    // 统计表信息
    $countResult = $pdo->query("SELECT COUNT(*) as total FROM videos");
    $total = $countResult->fetchColumn();
    echo "\n📊 视频记录总数: {$total}\n";
    
} catch (Exception $e) {
    echo "❌ 操作失败: " . $e->getMessage() . "\n";
    exit(1);
}
?>
