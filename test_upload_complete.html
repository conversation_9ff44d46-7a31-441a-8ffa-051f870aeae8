<!DOCTYPE html>
<html>
<head>
    <title>测试分片上传完成接口</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>测试分片上传完成接口</h1>
    <button onclick="testUploadComplete()">测试接口</button>
    <div id="result"></div>

    <script>
    async function testUploadComplete() {
        const resultDiv = document.getElementById('result');
        resultDiv.innerHTML = '测试中...';
        
        try {
            const response = await fetch('http://localhost:3000/api/v1/upload/chunked/complete', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-API-Key': 'ShiPinUser2024ProductionKey32Bytes!@#$%^&*()_+='
                },
                body: JSON.stringify({
                    upload_id: 'test123',
                    title: '测试视频',
                    video_type: 'short',
                    category_id: 1
                })
            });
            
            const data = await response.json();
            resultDiv.innerHTML = `
                <h3>响应状态: ${response.status}</h3>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
        } catch (error) {
            resultDiv.innerHTML = `<h3>错误: ${error.message}</h3>`;
        }
    }
    </script>
</body>
</html>
