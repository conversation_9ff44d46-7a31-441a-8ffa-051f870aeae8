# 🎬 企业级视频平台

> 基于现代化技术栈构建的全功能视频平台，支持短视频、长视频和直播功能

[![License](https://img.shields.io/badge/license-MIT-blue.svg)](LICENSE)
[![PHP Version](https://img.shields.io/badge/php-%3E%3D8.2-blue.svg)](https://php.net/)
[![Node Version](https://img.shields.io/badge/node-%3E%3D18.0-green.svg)](https://nodejs.org/)
[![Docker](https://img.shields.io/badge/docker-supported-blue.svg)](https://docker.com/)

## ✨ 项目特色

- 🚀 **现代化架构**: 前后端分离，微服务化设计
- 🎥 **多媒体支持**: 短视频、长视频、直播全覆盖
- 🔒 **企业级安全**: JWT认证、权限控制、视频加密
- 📊 **智能分析**: 用户行为分析、内容推荐算法
- 🕷️ **内容采集**: 多源内容自动采集和处理
- 📱 **响应式设计**: 完美适配PC和移动端
- 🐳 **容器化部署**: Docker一键部署，支持集群
- 📈 **性能监控**: 实时性能监控和告警
- 🎬 **视频处理**: 自动转码、切片、加密、缩略图生成
- 🛡️ **广告过滤**: 智能广告检测和自动跳过
- 🔄 **状态管理**: 完整的视频处理状态跟踪
- 👥 **用户管理**: 完善的用户权限和审核系统

## 🏗️ 技术架构

### 后端技术栈
- **PHP 8.2** + **ThinkPHP 8.0** - 现代化PHP框架
- **MySQL 8.0** - 高性能关系型数据库
- **Redis 7.0** - 内存数据库，缓存和队列
- **Nginx** - 高性能Web服务器
- **FFmpeg** - 视频处理和转码

### 前端技术栈
- **Vue 3.5** + **TypeScript 5.7** - 现代化前端框架
- **Element Plus** - 企业级UI组件库
- **Vite 6.0** - 现代化构建工具
- **Pinia** - Vue 3状态管理
- **Video.js** - 专业视频播放器

### 开发工具
- **Docker** + **Docker Compose** - 容器化开发和部署
- **PHPUnit** - PHP单元测试框架
- **Vitest** - 现代化JavaScript测试框架
- **ESLint** + **Prettier** - 代码质量和格式化

## 🎯 核心功能

### 📹 视频管理
- **视频上传**: 支持多种格式的视频文件上传
- **视频采集**: 自动从多个源采集视频内容
- **视频处理**: 自动转码、切片、生成缩略图
- **视频加密**: HLS加密保护视频内容安全
- **状态跟踪**: 完整的视频处理状态监控

### 👤 用户系统
- **用户注册/登录**: 支持邮箱、手机号注册
- **权限管理**: 基于角色的权限控制系统
- **个人中心**: 用户资料管理、作品管理
- **观看历史**: 记录用户观看行为和偏好

### 🎮 播放器功能
- **HLS播放**: 支持自适应码率流媒体播放
- **广告跳过**: 智能检测并自动跳过广告
- **播放控制**: 倍速播放、画质切换、全屏播放
- **弹幕系统**: 实时弹幕互动功能

### 🛠️ 管理后台
- **视频审核**: 管理员视频内容审核系统
- **用户管理**: 用户信息管理和权限分配
- **系统设置**: 全局配置和参数管理
- **数据统计**: 用户行为和系统性能统计

### 🔐 安全特性
- **JWT认证**: 无状态的用户认证机制
- **API安全**: 接口访问频率限制和权限验证
- **视频加密**: 基于AES的视频内容加密
- **数据保护**: 敏感数据加密存储

## 🚀 快速开始

### 环境要求

| 软件 | 版本要求 |
|------|----------|
| Docker | 20.10+ |
| Docker Compose | 2.0+ |
| PHP | 8.2+ |
| Node.js | 18.0+ |
| MySQL | 8.0+ |
| Redis | 7.0+ |

### 一键启动

```bash
# 克隆项目
git clone https://github.com/your-username/zhengshiban.git
cd zhengshiban

# Windows用户
start.bat

# Linux/macOS用户
chmod +x start.sh
./start.sh
```

### 访问应用

启动成功后，您可以访问：

- **用户前端**: http://localhost:3002
- **管理后台**: http://localhost:3001
- **API接口**: http://localhost:3000

### 默认账号

| 类型 | 用户名 | 密码 |
|------|--------|------|
| 管理员 | admin | admin123 |
| 测试用户 | testuser | password123 |

### 配置说明

#### 环境变量配置

复制 `.env.example` 到 `.env` 并根据需要修改配置：

```bash
# 数据库配置
DB_HOST=mysql
DB_PORT=3306
DB_NAME=zhengshiban_dev
DB_USER=root
DB_PASS=root123456

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# API配置
API_KEY=ShiPinAdmin2024ProductionKey32Bytes!@#$%^&*()_+
JWT_SECRET=your-jwt-secret-key

# 文件存储配置
UPLOAD_PATH=/app/storage/uploads
VIDEO_PATH=/app/storage/videos
```

#### 系统设置

首次启动后，请访问管理后台的系统设置页面配置：

1. **视频处理设置**
   - FFmpeg路径配置
   - 视频质量和码率设置
   - HLS分片时长配置

2. **安全设置**
   - 视频加密开关
   - 密钥轮换策略
   - 访问权限控制

3. **播放器设置**
   - 广告跳过功能
   - 播放器主题配置
   - 弹幕系统设置

## 📁 项目结构

```
zhengshiban/
├── packages/                  # 主要包目录
│   ├── api/                  # 后端API服务 (PHP + ThinkPHP)
│   ├── admin/                # 管理后台 (Vue 3 + TypeScript)
│   └── frontend/             # 用户前端 (Vue 3 + TypeScript)
├── database_setup/           # 数据库脚本和迁移文件
├── docker/                   # Docker配置文件
├── docs/                     # 项目文档
├── scripts/                  # 构建和部署脚本
├── tests/                    # 测试文件
├── docker-compose.yml        # Docker编排文件
└── README.md                 # 项目说明文档
```

## 🎯 核心功能

### 用户端功能
- ✅ 用户注册/登录/找回密码
- ✅ 个人资料管理和头像上传
- ✅ 视频浏览、搜索和分类筛选
- ✅ 视频播放（多清晰度、倍速播放）
- ✅ 视频点赞、评论、收藏
- ✅ VIP会员系统
- ✅ 个人中心和观看历史

### 管理端功能
- ✅ 系统仪表盘和数据统计
- ✅ 用户管理（用户列表、状态管理、VIP管理）
- ✅ 视频管理（视频审核、分类管理、批量操作）
- ✅ 内容采集（多源采集、自动去重、定时任务）
- ✅ 系统设置（网站配置、播放器设置、上传配置）
- ✅ 安全管理（权限控制、操作日志、会话管理）
- ✅ 性能监控（系统监控、API监控、错误日志）

### 技术特性
- ✅ RESTful API设计
- ✅ JWT Token认证
- ✅ Redis缓存优化
- ✅ 数据库查询优化
- ✅ 文件分片上传
- ✅ 视频转码和HLS切片
- ✅ 响应式设计
- ✅ 国际化支持

## 📖 文档

- [📋 API文档](API_DOCUMENTATION.md) - 详细的API接口文档
- [🚀 部署指南](DEPLOYMENT_GUIDE.md) - 完整的部署和运维指南
- [👨‍💻 开发者指南](DEVELOPER_GUIDE.md) - 开发规范和最佳实践
- [📊 项目分析报告](PROJECT_ANALYSIS_REPORT.md) - 详细的技术分析报告
- [📝 项目总结](PROJECT_SUMMARY.md) - 项目概览和评估结果

## 🧪 测试

```bash
# 运行后端测试
cd packages/api
php run-tests.php

# 运行前端测试
cd packages/admin
npm run test

cd packages/frontend
npm run test
```

## 📊 性能指标

- **API响应时间**: < 200ms (95%分位数)
- **页面加载时间**: < 2s (首屏)
- **并发用户**: 1000+ (单机)
- **视频上传**: 支持10GB+大文件
- **缓存命中率**: > 90%

## 🔒 安全特性

- **认证安全**: JWT Token + 刷新机制
- **数据安全**: Argon2id密码哈希
- **传输安全**: HTTPS + HSTS
- **输入验证**: 统一参数验证和过滤
- **权限控制**: 基于角色的访问控制(RBAC)
- **安全头**: CSP、XSS保护、CSRF防护

## 📚 API文档

### 认证接口

```bash
# 用户登录
POST /api/auth/login
Content-Type: application/json
{
  "username": "testuser",
  "password": "password123"
}

# 管理员登录
POST /api/admin/auth/login
Content-Type: application/json
{
  "username": "admin",
  "password": "admin123"
}
```

### 视频接口

```bash
# 获取视频列表
GET /api/videos?page=1&limit=20

# 获取视频详情
GET /api/videos/{id}

# 上传视频
POST /api/videos/upload
Content-Type: multipart/form-data

# 获取视频处理状态
GET /api/video-processing/{id}/status
```

### 管理员接口

```bash
# 获取待审核视频
GET /api/admin/videos?status=pending

# 审核视频
PUT /api/admin/videos/{id}/audit
Content-Type: application/json
{
  "audit_status": "approved",
  "audit_comment": "审核通过"
}

# 系统设置
GET /api/admin/settings
PUT /api/admin/settings
```

## 🌟 贡献指南

我们欢迎所有形式的贡献！请查看 [贡献指南](CONTRIBUTING.md) 了解详情。

### 开发流程

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢以下开源项目：

- [ThinkPHP](https://www.thinkphp.cn/) - 优秀的PHP框架
- [Vue.js](https://vuejs.org/) - 渐进式JavaScript框架
- [Element Plus](https://element-plus.org/) - Vue 3 UI组件库
- [Video.js](https://videojs.com/) - HTML5视频播放器
- [Docker](https://www.docker.com/) - 容器化平台

## 📞 联系我们

- **项目主页**: https://github.com/your-username/zhengshiban
- **问题反馈**: https://github.com/your-username/zhengshiban/issues
- **技术支持**: <EMAIL>
- **商务合作**: <EMAIL>

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！

**Made with ❤️ by ZhengShiBan Team**

## 🔧 常用命令

```bash
# 查看服务状态
docker-compose ps

# 查看日志（实时）
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f api
docker-compose logs -f admin
docker-compose logs -f frontend

# 停止服务
docker-compose down

# 重启服务
docker-compose restart

# 重新构建镜像（标准方式）
docker-compose build

# 优化构建（推荐，避免重复下载FFmpeg）
bash scripts/build-optimized.sh --use-optimized

# 进入容器调试
docker exec -it shipin-api bash
docker exec -it shipin-admin sh
docker exec -it shipin-frontend sh
```

## 🐛 开发调试

- **API调试**: 修改 `packages/api/` 下的PHP代码，自动生效
- **前端调试**: 修改 `packages/admin/` 或 `packages/frontend/` 下的Vue代码，热更新
- **数据库**: 数据持久化保存，重启不丢失
- **日志查看**: `docker-compose logs -f [服务名]`

## 📋 系统要求

- Docker 和 Docker Compose
- 4GB+ 内存 (开发模式需要更多内存)
- 20GB+ 磁盘空间

---

🎯 **开发模式：代码修改即时生效，完美的开发体验！**
