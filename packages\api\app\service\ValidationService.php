<?php
declare (strict_types = 1);

namespace app\service;

use think\facade\Validate;

/**
 * 验证服务
 * 
 * 提供统一的参数验证功能
 * 
 * <AUTHOR> Platform Team
 * @version 1.0
 */
class ValidationService
{
    /**
     * 用户登录参数验证规则
     *
     * @return array
     */
    public function getLoginRules(): array
    {
        return [
            'username' => 'require',
            'password' => 'require|min:6'
        ];
    }

    /**
     * 用户登录参数验证消息
     *
     * @return array
     */
    public function getLoginMessages(): array
    {
        return [
            'username.require' => '用户名不能为空',
            'password.require' => '密码不能为空',
            'password.min' => '密码长度不能少于6位'
        ];
    }
    /**
     * 视频列表查询参数验证规则
     * 
     * @return array
     */
    public function getVideoListRules(): array
    {
        return [
            'page' => 'integer|egt:1',
            'limit' => 'integer|between:1,100',
            'video_type' => 'in:long,short',
            'category_id' => 'integer|gt:0',
            'user_id' => 'integer|gt:0',
            'sort' => 'in:latest,popular,likes,engagement_score'
        ];
    }

    /**
     * 视频列表查询参数验证消息
     * 
     * @return array
     */
    public function getVideoListMessages(): array
    {
        return [
            'page.integer' => '页码必须是整数',
            'page.egt' => '页码必须大于等于1',
            'limit.integer' => '每页数量必须是整数',
            'limit.between' => '每页数量必须在1-100之间',
            'video_type.in' => '视频类型必须是long或short',
            'category_id.integer' => '分类ID必须是整数',
            'category_id.gt' => '分类ID必须大于0',
            'user_id.integer' => '用户ID必须是整数',
            'user_id.gt' => '用户ID必须大于0',
            'sort.in' => '排序方式无效'
        ];
    }

    /**
     * 视频创建参数验证规则
     * 
     * @return array
     */
    public function getVideoCreateRules(): array
    {
        return [
            'title' => 'require|length:1,200',
            'description' => 'max:2000',
            'video_type' => 'require|in:long,short',
            'category_id' => 'require|integer|gt:0',
            'file_path' => 'require',
            'cover_image' => 'max:500',
            'duration' => 'integer|egt:0',
            'file_size' => 'integer|egt:0',
            'width' => 'integer|egt:0',
            'height' => 'integer|egt:0',
            'format' => 'max:10',
            'is_free' => 'in:0,1',
            'points_price' => 'integer|egt:0'
        ];
    }

    /**
     * 视频创建参数验证消息
     * 
     * @return array
     */
    public function getVideoCreateMessages(): array
    {
        return [
            'title.require' => '视频标题不能为空',
            'title.length' => '标题长度为1-200个字符',
            'description.max' => '描述不能超过2000个字符',
            'video_type.require' => '视频类型不能为空',
            'video_type.in' => '视频类型必须是long或short',
            'category_id.require' => '分类不能为空',
            'category_id.integer' => '分类ID必须是整数',
            'category_id.gt' => '分类ID必须大于0',
            'file_path.require' => '视频文件不能为空',
            'cover_image.max' => '封面图片路径过长',
            'duration.integer' => '视频时长必须是整数',
            'duration.egt' => '视频时长不能为负数',
            'file_size.integer' => '文件大小必须是整数',
            'file_size.egt' => '文件大小不能为负数',
            'width.integer' => '视频宽度必须是整数',
            'width.egt' => '视频宽度不能为负数',
            'height.integer' => '视频高度必须是整数',
            'height.egt' => '视频高度不能为负数',
            'format.max' => '视频格式字符串过长',
            'is_free.in' => '免费标识必须是0或1',
            'points_price.integer' => '积分价格必须是整数',
            'points_price.egt' => '积分价格不能为负数'
        ];
    }

    /**
     * 视频更新参数验证规则
     * 
     * @return array
     */
    public function getVideoUpdateRules(): array
    {
        return [
            'title' => 'length:1,200',
            'description' => 'max:2000',
            'video_type' => 'in:long,short',
            'category_id' => 'integer|gt:0',
            'cover_image' => 'max:500',
            'is_free' => 'in:0,1',
            'points_price' => 'integer|egt:0'
        ];
    }

    /**
     * 视频更新参数验证消息
     * 
     * @return array
     */
    public function getVideoUpdateMessages(): array
    {
        return [
            'title.length' => '标题长度为1-200个字符',
            'description.max' => '描述不能超过2000个字符',
            'video_type.in' => '视频类型必须是long或short',
            'category_id.integer' => '分类ID必须是整数',
            'category_id.gt' => '分类ID必须大于0',
            'cover_image.max' => '封面图片路径过长',
            'is_free.in' => '免费标识必须是0或1',
            'points_price.integer' => '积分价格必须是整数',
            'points_price.egt' => '积分价格不能为负数'
        ];
    }

    /**
     * 搜索参数验证规则
     * 
     * @return array
     */
    public function getSearchRules(): array
    {
        return [
            'keyword' => 'require|length:1,100',
            'page' => 'integer|egt:1',
            'limit' => 'integer|between:1,100',
            'video_type' => 'in:long,short'
        ];
    }

    /**
     * 搜索参数验证消息
     * 
     * @return array
     */
    public function getSearchMessages(): array
    {
        return [
            'keyword.require' => '搜索关键词不能为空',
            'keyword.length' => '搜索关键词长度为1-100个字符',
            'page.integer' => '页码必须是整数',
            'page.egt' => '页码必须大于等于1',
            'limit.integer' => '每页数量必须是整数',
            'limit.between' => '每页数量必须在1-100之间',
            'video_type.in' => '视频类型必须是long或short'
        ];
    }

    /**
     * 验证数据
     * 
     * @param array $data 待验证数据
     * @param array $rules 验证规则
     * @param array $messages 验证消息
     * @return array|true 验证结果，true表示通过，数组表示错误信息
     */
    public function validate(array $data, array $rules, array $messages = []): array|bool
    {
        $validate = Validate::rule($rules)->message($messages);
        
        if (!$validate->check($data)) {
            return [
                'error' => $validate->getError(),
                'errors' => $validate->getError()
            ];
        }
        
        return true;
    }

    /**
     * 清理和标准化分页参数
     * 
     * @param array $params 原始参数
     * @return array 标准化后的参数
     */
    public function normalizePaginationParams(array $params): array
    {
        return [
            'page' => max(1, (int)($params['page'] ?? 1)),
            'limit' => min(100, max(1, (int)($params['limit'] ?? 20)))
        ];
    }

    /**
     * 清理和标准化排序参数
     *
     * @param string $sort 排序参数
     * @return string 标准化后的排序SQL
     */
    public function normalizeSortParam(string $sort = ''): string
    {
        $sortMap = [
            'latest' => 'v.created_at desc',
            'popular' => 'v.view_count desc',
            'likes' => 'v.like_count desc',
            'engagement_score' => 'v.engagement_score desc, v.view_count desc'
        ];

        return $sortMap[$sort] ?? $sortMap['latest'];
    }

    /**
     * 分类列表查询参数验证规则
     *
     * @return array
     */
    public function getCategoryListRules(): array
    {
        return [
            'video_type' => 'in:long,short,collection',
            'parent_id' => 'integer|egt:0',
            'tree' => 'in:0,1'
        ];
    }

    /**
     * 分类列表查询参数验证消息
     *
     * @return array
     */
    public function getCategoryListMessages(): array
    {
        return [
            'video_type.in' => '视频类型必须是long、short或collection',
            'parent_id.integer' => '父分类ID必须是整数',
            'parent_id.egt' => '父分类ID不能为负数',
            'tree.in' => '树形结构参数必须是0或1'
        ];
    }

    /**
     * 高级验证：批量验证多个数据集
     *
     * @param array $datasets 多个数据集
     * @param array $rules 验证规则
     * @param array $messages 验证消息
     * @return array 验证结果
     */
    public function validateBatch(array $datasets, array $rules, array $messages = []): array
    {
        $results = [];
        $hasErrors = false;

        foreach ($datasets as $index => $data) {
            $result = $this->validate($data, $rules, $messages);

            if ($result !== true) {
                $hasErrors = true;
                $results[$index] = $result;
            }
        }

        return [
            'success' => !$hasErrors,
            'errors' => $results,
            'total' => count($datasets),
            'failed' => count($results)
        ];
    }

    /**
     * 智能验证：根据数据类型自动选择验证规则
     *
     * @param array $data 待验证数据
     * @param string $type 数据类型 (user, video, category等)
     * @return array|bool 验证结果
     */
    public function smartValidate(array $data, string $type): array|bool
    {
        $ruleMethod = 'get' . ucfirst($type) . 'Rules';
        $messageMethod = 'get' . ucfirst($type) . 'Messages';

        if (!method_exists($this, $ruleMethod)) {
            return [
                'error' => "未找到类型 '{$type}' 的验证规则",
                'errors' => ['type' => '无效的验证类型']
            ];
        }

        $rules = $this->$ruleMethod();
        $messages = method_exists($this, $messageMethod) ? $this->$messageMethod() : [];

        return $this->validate($data, $rules, $messages);
    }

    /**
     * 条件验证：根据条件动态调整验证规则
     *
     * @param array $data 待验证数据
     * @param array $baseRules 基础验证规则
     * @param array $conditions 条件规则
     * @param array $messages 验证消息
     * @return array|bool 验证结果
     */
    public function conditionalValidate(array $data, array $baseRules, array $conditions, array $messages = []): array|bool
    {
        $finalRules = $baseRules;

        // 根据条件添加额外规则
        foreach ($conditions as $condition => $additionalRules) {
            if ($this->evaluateCondition($data, $condition)) {
                $finalRules = array_merge($finalRules, $additionalRules);
            }
        }

        return $this->validate($data, $finalRules, $messages);
    }

    /**
     * 评估条件
     *
     * @param array $data 数据
     * @param string $condition 条件表达式
     * @return bool 条件是否满足
     */
    private function evaluateCondition(array $data, string $condition): bool
    {
        // 简单的条件评估，可以扩展为更复杂的表达式解析
        if (strpos($condition, '=') !== false) {
            [$field, $value] = explode('=', $condition, 2);
            return isset($data[trim($field)]) && $data[trim($field)] == trim($value);
        }

        if (strpos($condition, 'isset:') === 0) {
            $field = substr($condition, 6);
            return isset($data[$field]);
        }

        if (strpos($condition, 'empty:') === 0) {
            $field = substr($condition, 6);
            return empty($data[$field]);
        }

        return false;
    }

    /**
     * 数据清理：移除不需要的字段并格式化数据
     *
     * @param array $data 原始数据
     * @param array $allowedFields 允许的字段
     * @param array $formatters 字段格式化器
     * @return array 清理后的数据
     */
    public function sanitizeData(array $data, array $allowedFields = [], array $formatters = []): array
    {
        $sanitized = [];

        // 如果指定了允许的字段，只保留这些字段
        if (!empty($allowedFields)) {
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $sanitized[$field] = $data[$field];
                }
            }
        } else {
            $sanitized = $data;
        }

        // 应用格式化器
        foreach ($formatters as $field => $formatter) {
            if (isset($sanitized[$field])) {
                $sanitized[$field] = $this->applyFormatter($sanitized[$field], $formatter);
            }
        }

        return $sanitized;
    }

    /**
     * 应用格式化器
     *
     * @param mixed $value 原始值
     * @param string|callable $formatter 格式化器
     * @return mixed 格式化后的值
     */
    private function applyFormatter($value, $formatter)
    {
        if (is_callable($formatter)) {
            return $formatter($value);
        }

        switch ($formatter) {
            case 'trim':
                return is_string($value) ? trim($value) : $value;
            case 'lower':
                return is_string($value) ? strtolower($value) : $value;
            case 'upper':
                return is_string($value) ? strtoupper($value) : $value;
            case 'int':
                return (int)$value;
            case 'float':
                return (float)$value;
            case 'bool':
                return (bool)$value;
            case 'strip_tags':
                return is_string($value) ? strip_tags($value) : $value;
            default:
                return $value;
        }
    }

    /**
     * 获取验证统计信息
     *
     * @return array 统计信息
     */
    public function getValidationStats(): array
    {
        // 这里可以实现验证统计功能
        return [
            'total_validations' => 0,
            'successful_validations' => 0,
            'failed_validations' => 0,
            'most_common_errors' => [],
            'performance_metrics' => [
                'avg_validation_time' => 0,
                'slowest_validation' => 0
            ]
        ];
    }
}
