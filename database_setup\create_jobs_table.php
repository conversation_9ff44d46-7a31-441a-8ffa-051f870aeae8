<?php
/**
 * 创建队列任务表
 * 修复视频处理队列缺少jobs表的问题
 */

$host = 'mysql';
$username = 'root';
$password = 'root123456';
$database = 'zhengshiban_dev';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database;charset=utf8mb4", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci"
    ]);
    
    echo "数据库连接成功\n";
    
    // 检查jobs表是否存在
    $checkSql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
                 WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'jobs'";
    $stmt = $pdo->prepare($checkSql);
    $stmt->execute([$database]);
    $exists = $stmt->fetchColumn() > 0;
    
    if ($exists) {
        echo "⚠️ jobs表已存在，跳过创建\n";
    } else {
        echo "创建jobs表...\n";
        
        $createJobsTable = "
        CREATE TABLE `jobs` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT,
            `queue` varchar(255) NOT NULL COMMENT '队列名称',
            `payload` longtext NOT NULL COMMENT '任务数据',
            `attempts` tinyint unsigned NOT NULL DEFAULT '0' COMMENT '尝试次数',
            `reserved_at` int unsigned DEFAULT NULL COMMENT '保留时间',
            `available_at` int unsigned NOT NULL COMMENT '可用时间',
            `created_at` int unsigned NOT NULL COMMENT '创建时间',
            PRIMARY KEY (`id`),
            KEY `jobs_queue_index` (`queue`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='队列任务表';
        ";
        
        $pdo->exec($createJobsTable);
        echo "✅ jobs表创建成功\n";
    }
    
    // 检查failed_jobs表是否存在
    $checkFailedSql = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
                       WHERE TABLE_SCHEMA = ? AND TABLE_NAME = 'failed_jobs'";
    $stmt = $pdo->prepare($checkFailedSql);
    $stmt->execute([$database]);
    $failedExists = $stmt->fetchColumn() > 0;
    
    if ($failedExists) {
        echo "⚠️ failed_jobs表已存在，跳过创建\n";
    } else {
        echo "创建failed_jobs表...\n";
        
        $createFailedJobsTable = "
        CREATE TABLE `failed_jobs` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT,
            `uuid` varchar(255) NOT NULL COMMENT '任务UUID',
            `connection` text NOT NULL COMMENT '连接信息',
            `queue` text NOT NULL COMMENT '队列名称',
            `payload` longtext NOT NULL COMMENT '任务数据',
            `exception` longtext NOT NULL COMMENT '异常信息',
            `failed_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '失败时间',
            PRIMARY KEY (`id`),
            UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='失败任务表';
        ";
        
        $pdo->exec($createFailedJobsTable);
        echo "✅ failed_jobs表创建成功\n";
    }
    
    // 显示表结构
    echo "\n📋 jobs表结构:\n";
    $result = $pdo->query("DESCRIBE jobs");
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo sprintf("%-15s %-20s %-10s %-10s %-15s %s\n", 
            $row['Field'], 
            $row['Type'], 
            $row['Null'], 
            $row['Key'], 
            $row['Default'], 
            $row['Extra']
        );
    }
    
    // 统计表信息
    $countResult = $pdo->query("SELECT COUNT(*) as total FROM jobs");
    $total = $countResult->fetchColumn();
    echo "\n📊 队列任务总数: {$total}\n";
    
    echo "\n✅ 队列表创建完成\n";
    
} catch (Exception $e) {
    echo "❌ 操作失败: " . $e->getMessage() . "\n";
    exit(1);
}
?>
