<?php
declare(strict_types=1);

namespace app\service;

use think\facade\Cache;
use think\facade\Db;
use think\facade\Log;
use think\facade\Config;

/**
 * 高级性能缓存服务 - 企业级缓存策略实现
 *
 * 功能特性：
 * - 多级缓存策略 (L1内存 + L2Redis + L3文件)
 * - 智能缓存预热和失效
 * - 缓存命中率统计
 * - 缓存压缩优化
 * - 标签化缓存管理
 * - 防缓存雪崩机制
 *
 * <AUTHOR> Platform Team
 * @version 2.1
 */
class PerformanceCacheService
{
    // 缓存键前缀
    private const CACHE_PREFIX = 'perf_cache:';

    // 缓存层级定义
    private const CACHE_LEVELS = [
        'L1' => 'memory',    // 内存缓存 (最快，容量小)
        'L2' => 'redis',     // Redis缓存 (快，容量中)
        'L3' => 'file',      // 文件缓存 (慢，容量大)
    ];

    // 缓存时间配置（秒）
    private const CACHE_TIMES = [
        // 热点数据 - 短时间缓存，高频访问
        'video_list' => 300,        // 视频列表缓存5分钟
        'hot_videos' => 300,        // 热门视频缓存5分钟
        'trending_tags' => 600,     // 热门标签缓存10分钟
        'dashboard_stats' => 300,   // 仪表盘统计缓存5分钟

        // 常规数据 - 中等时间缓存
        'video_detail' => 1800,     // 视频详情缓存30分钟
        'user_stats' => 900,        // 用户统计缓存15分钟
        'category_list' => 1800,    // 分类列表缓存30分钟
        'user_profile' => 3600,     // 用户资料缓存1小时

        // 稳定数据 - 长时间缓存
        'system_config' => 7200,    // 系统配置缓存2小时
        'category_tree' => 14400,   // 分类树缓存4小时
        'static_content' => 86400,  // 静态内容缓存24小时
    ];

    // 缓存标签分组
    private const CACHE_TAGS = [
        'videos' => ['video_list', 'video_detail', 'hot_videos'],
        'users' => ['user_stats', 'user_profile', 'dashboard_stats'],
        'categories' => ['category_list', 'category_tree'],
        'system' => ['system_config', 'static_content'],
    ];

    // 内存缓存存储 (简单实现)
    private static array $memoryCache = [];

    // 缓存统计
    private array $stats = [
        'hits' => 0,
        'misses' => 0,
        'sets' => 0,
        'deletes' => 0,
        'compressions' => 0,
    ];

    /**
     * 多级缓存获取方法
     */
    public function getMultiLevel(string $key, callable $callback = null, string $preferredLevel = 'L2')
    {
        $this->logCacheOperation('get', $key, $preferredLevel);

        // 尝试从首选级别获取
        $value = $this->getFromLevel($key, $preferredLevel);
        if ($value !== null) {
            $this->stats['hits']++;
            return $this->decompressIfNeeded($value);
        }

        // 尝试从其他级别获取
        foreach (self::CACHE_LEVELS as $level => $store) {
            if ($level === $preferredLevel) continue;

            $value = $this->getFromLevel($key, $level);
            if ($value !== null) {
                // 回写到首选级别
                $this->setToLevel($key, $value, $preferredLevel);
                $this->stats['hits']++;
                return $this->decompressIfNeeded($value);
            }
        }

        // 如果有回调函数，执行并缓存结果
        if ($callback && is_callable($callback)) {
            $value = $callback();
            $this->setMultiLevel($key, $value, null, $preferredLevel);
            return $value;
        }

        $this->stats['misses']++;
        return null;
    }

    /**
     * 多级缓存设置方法
     */
    public function setMultiLevel(string $key, $value, ?int $ttl = null, string $level = 'L2'): bool
    {
        $this->logCacheOperation('set', $key, $level);

        try {
            if ($ttl === null) {
                $ttl = $this->getDefaultTTL($key);
            }

            // 大数据压缩
            $compressedValue = $this->compressIfNeeded($value);

            $result = $this->setToLevel($key, $compressedValue, $level, $ttl);

            if ($result) {
                $this->stats['sets']++;
                $this->setCacheTags($key);
            }

            return $result;

        } catch (\Exception $e) {
            Log::error('多级缓存设置失败', [
                'key' => $key,
                'level' => $level,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 获取视频列表（优化版，支持多级缓存）
     */
    public function getVideoList(array $params = []): array
    {
        $cacheKey = $this->generateCacheKey('video_list', $params);

        return $this->getMultiLevel($cacheKey, function () use ($params) {
            // 使用优化的查询，添加索引提示
            $query = Db::table('videos')
                ->alias('v')
                ->leftJoin('users u', 'v.user_id = u.id')
                ->leftJoin('video_categories c', 'v.category_id = c.id')
                ->field([
                    'v.id', 'v.title', 'v.description', 'v.cover_image',
                    'v.duration', 'v.view_count', 'v.like_count', 'v.is_vip',
                    'v.created_at', 'u.username', 'u.nickname', 'c.name as category_name'
                ])
                ->where('v.status', 'published')
                ->where('v.audit_status', 'approved');

            // 优化的筛选条件
            if (!empty($params['category_id'])) {
                $query->where('v.category_id', $params['category_id']);
            }

            if (!empty($params['video_type'])) {
                $query->where('v.video_type', $params['video_type']);
            }

            if (!empty($params['keyword'])) {
                // 使用全文搜索优化
                $query->where('MATCH(v.title, v.description) AGAINST(? IN BOOLEAN MODE)', [$params['keyword']]);
            }

            $page = max(1, $params['page'] ?? 1);
            $limit = min($params['limit'] ?? 20, 50);
            $offset = ($page - 1) * $limit;

            // 分离计数查询以提高性能
            $countQuery = clone $query;
            $total = $countQuery->count();

            // 添加排序优化
            $orderBy = $params['order_by'] ?? 'created_at';
            $orderDir = $params['order_dir'] ?? 'desc';

            $videos = $query->order("v.{$orderBy}", $orderDir)
                ->limit($offset, $limit)
                ->select()
                ->toArray();

            return [
                'videos' => $videos,
                'total' => $total,
                'page' => $page,
                'limit' => $limit,
                'pages' => ceil($total / $limit),
                'cached_at' => time()
            ];
        }, 'L2');
    }

    /**
     * 获取视频详情（带缓存）
     */
    public function getVideoDetail(int $videoId): ?array
    {
        $cacheKey = $this->generateCacheKey('video_detail', ['id' => $videoId]);
        
        return Cache::remember($cacheKey, function () use ($videoId) {
            $video = Db::table('videos')
                ->alias('v')
                ->leftJoin('users u', 'v.user_id = u.id')
                ->leftJoin('video_categories c', 'v.category_id = c.id')
                ->field([
                    'v.*',
                    'u.username', 'u.nickname', 'u.avatar as user_avatar',
                    'c.name as category_name'
                ])
                ->where('v.id', $videoId)
                ->where('v.status', 'published')
                ->where('v.audit_status', 'approved')
                ->find();

            if (!$video) {
                return null;
            }

            // 获取播放地址
            $playUrls = Db::table('video_play_urls')
                ->where('video_id', $videoId)
                ->order('id', 'asc')
                ->select()
                ->toArray();

            $video['play_urls'] = $playUrls;
            return $video;
        }, self::CACHE_TIMES['video_detail']);
    }

    /**
     * 获取分类列表（带缓存）
     */
    public function getCategoryList(array $params = []): array
    {
        $cacheKey = $this->generateCacheKey('category_list', $params);
        
        return Cache::remember($cacheKey, function () use ($params) {
            $query = Db::table('video_categories')
                ->where('status', 1);

            if (!empty($params['video_type'])) {
                $query->where('video_type', $params['video_type']);
            }

            if (!empty($params['parent_id'])) {
                $query->where('parent_id', $params['parent_id']);
            }

            return $query->order('sort_order asc, id asc')
                ->select()
                ->toArray();
        }, self::CACHE_TIMES['category_list']);
    }

    /**
     * 获取热门视频（带缓存）
     */
    public function getHotVideos(int $limit = 10): array
    {
        $cacheKey = $this->generateCacheKey('hot_videos', ['limit' => $limit]);
        
        return Cache::remember($cacheKey, function () use ($limit) {
            return Db::table('videos')
                ->alias('v')
                ->leftJoin('users u', 'v.user_id = u.id')
                ->leftJoin('video_categories c', 'v.category_id = c.id')
                ->field([
                    'v.id', 'v.title', 'v.cover_image', 'v.duration',
                    'v.view_count', 'v.like_count', 'v.is_vip',
                    'u.username', 'u.nickname', 'c.name as category_name'
                ])
                ->where('v.status', 'published')
                ->where('v.audit_status', 'approved')
                ->where('v.view_count', '>', 0)
                ->orderRaw('v.view_count DESC, v.like_count DESC')
                ->limit($limit)
                ->select()
                ->toArray();
        }, self::CACHE_TIMES['hot_videos']);
    }

    /**
     * 获取用户统计信息（带缓存）
     */
    public function getUserStats(int $userId): array
    {
        $cacheKey = $this->generateCacheKey('user_stats', ['user_id' => $userId]);
        
        return Cache::remember($cacheKey, function () use ($userId) {
            return [
                'video_count' => Db::table('videos')
                    ->where('user_id', $userId)
                    ->where('status', 'published')
                    ->count(),
                'total_views' => Db::table('videos')
                    ->where('user_id', $userId)
                    ->sum('view_count'),
                'total_likes' => Db::table('videos')
                    ->where('user_id', $userId)
                    ->sum('like_count'),
                'favorite_count' => Db::table('video_collections')
                    ->where('user_id', $userId)
                    ->count()
            ];
        }, self::CACHE_TIMES['user_stats']);
    }

    /**
     * 获取仪表盘统计数据（带缓存）
     */
    public function getDashboardStats(): array
    {
        $cacheKey = $this->generateCacheKey('dashboard_stats', []);
        
        return Cache::remember($cacheKey, function () {
            $today = date('Y-m-d');
            
            return [
                'total_users' => Db::table('users')->where('status', 'active')->count(),
                'today_new_users' => Db::table('users')
                    ->where('status', 'active')
                    ->whereTime('created_at', 'today')
                    ->count(),
                'total_videos' => Db::table('videos')
                    ->where('status', 'published')
                    ->count(),
                'today_new_videos' => Db::table('videos')
                    ->where('status', 'published')
                    ->whereTime('created_at', 'today')
                    ->count(),
                'total_views' => Db::table('videos')->sum('view_count'),
                'today_views' => Db::table('videos')
                    ->whereTime('updated_at', 'today')
                    ->sum('view_count'),
            ];
        }, self::CACHE_TIMES['dashboard_stats']);
    }

    /**
     * 获取系统配置（带缓存）
     */
    public function getSystemConfig(string $group = 'website'): array
    {
        $cacheKey = $this->generateCacheKey('system_config', ['group' => $group]);
        
        return Cache::remember($cacheKey, function () use ($group) {
            return Db::table('system_configs')
                ->where('group', $group)
                ->column('value', 'key');
        }, self::CACHE_TIMES['system_config']);
    }

    /**
     * 清除指定缓存
     */
    public function clearCache(string $type, array $params = []): bool
    {
        $cacheKey = $this->generateCacheKey($type, $params);
        return Cache::delete($cacheKey);
    }

    /**
     * 清除相关缓存（当数据更新时调用）
     */
    public function clearRelatedCache(string $type, int $id = 0): void
    {
        switch ($type) {
            case 'video':
                // 清除视频相关缓存
                $this->clearCacheByPattern('video_list:*');
                $this->clearCacheByPattern('video_detail:*');
                $this->clearCacheByPattern('hot_videos:*');
                if ($id > 0) {
                    $this->clearCache('video_detail', ['id' => $id]);
                }
                break;
                
            case 'category':
                // 清除分类相关缓存
                $this->clearCacheByPattern('category_list:*');
                $this->clearCacheByPattern('video_list:*');
                break;
                
            case 'user':
                // 清除用户相关缓存
                if ($id > 0) {
                    $this->clearCache('user_stats', ['user_id' => $id]);
                }
                $this->clearCacheByPattern('dashboard_stats:*');
                break;
                
            case 'system_config':
                // 清除系统配置缓存
                $this->clearCacheByPattern('system_config:*');
                break;
        }
    }

    /**
     * 生成缓存键
     */
    private function generateCacheKey(string $type, array $params): string
    {
        $paramStr = empty($params) ? '' : ':' . md5(serialize($params));
        return self::CACHE_PREFIX . $type . $paramStr;
    }

    /**
     * 按模式清除缓存
     */
    private function clearCacheByPattern(string $pattern): void
    {
        try {
            // 这里需要根据实际的Redis实现来清除匹配的键
            // 由于ThinkPHP的Cache门面可能不直接支持模式删除，
            // 这里记录日志，实际实现可能需要直接操作Redis
            Log::info("清除缓存模式: {$pattern}");
        } catch (\Exception $e) {
            Log::error("清除缓存失败: " . $e->getMessage());
        }
    }

    /**
     * 智能缓存预热
     */
    public function warmupCache(): void
    {
        Log::info("开始智能缓存预热");

        try {
            $startTime = microtime(true);

            // 预热热点数据 (L1内存缓存)
            $this->warmupHotData();

            // 预热常规数据 (L2Redis缓存)
            $this->warmupRegularData();

            // 预热稳定数据 (L3文件缓存)
            $this->warmupStableData();

            $endTime = microtime(true);
            $duration = round(($endTime - $startTime) * 1000, 2);

            Log::info("缓存预热完成", [
                'duration_ms' => $duration,
                'memory_usage' => memory_get_usage(true),
                'peak_memory' => memory_get_peak_usage(true)
            ]);

        } catch (\Exception $e) {
            Log::error("缓存预热失败: " . $e->getMessage());
        }
    }

    /**
     * 预热热点数据
     */
    private function warmupHotData(): void
    {
        // 热门视频 (内存缓存)
        $this->getMultiLevel(
            $this->generateCacheKey('hot_videos', ['limit' => 20]),
            function() {
                return $this->getHotVideos(20);
            },
            'L1'
        );

        // 实时统计 (内存缓存)
        $this->getMultiLevel(
            $this->generateCacheKey('dashboard_stats', []),
            function() {
                return $this->getDashboardStats();
            },
            'L1'
        );
    }

    /**
     * 预热常规数据
     */
    private function warmupRegularData(): void
    {
        // 分类列表
        foreach (['short', 'long', 'live'] as $videoType) {
            $this->getCategoryList(['video_type' => $videoType]);
        }

        // 视频列表首页
        $this->getVideoList(['page' => 1, 'limit' => 20]);
    }

    /**
     * 预热稳定数据
     */
    private function warmupStableData(): void
    {
        // 系统配置
        $this->getSystemConfig('website');
        $this->getSystemConfig('player');
        $this->getSystemConfig('upload');

        // 分类树结构
        $this->getMultiLevel(
            $this->generateCacheKey('category_tree', []),
            function() {
                return $this->buildCategoryTree();
            },
            'L3'
        );
    }

    /**
     * 获取缓存统计信息
     */
    public function getCacheStats(): array
    {
        $hitRate = ($this->stats['hits'] + $this->stats['misses']) > 0
            ? round($this->stats['hits'] / ($this->stats['hits'] + $this->stats['misses']) * 100, 2)
            : 0;

        return [
            'hits' => $this->stats['hits'],
            'misses' => $this->stats['misses'],
            'sets' => $this->stats['sets'],
            'deletes' => $this->stats['deletes'],
            'compressions' => $this->stats['compressions'],
            'hit_rate' => $hitRate . '%',
            'memory_cache_size' => count(self::$memoryCache),
            'memory_usage' => [
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true),
                'limit' => ini_get('memory_limit')
            ]
        ];
    }

    /**
     * 从指定级别获取缓存
     */
    private function getFromLevel(string $key, string $level)
    {
        try {
            switch (self::CACHE_LEVELS[$level] ?? 'redis') {
                case 'memory':
                    return $this->getFromMemory($key);
                case 'redis':
                    return Cache::store('redis')->get($key);
                case 'file':
                    return Cache::store('file')->get($key);
                default:
                    return Cache::get($key);
            }
        } catch (\Exception $e) {
            Log::warning("从缓存级别 {$level} 获取失败", [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 设置到指定级别
     */
    private function setToLevel(string $key, $value, string $level, int $ttl = 3600): bool
    {
        try {
            switch (self::CACHE_LEVELS[$level] ?? 'redis') {
                case 'memory':
                    return $this->setToMemory($key, $value, $ttl);
                case 'redis':
                    return Cache::store('redis')->set($key, $value, $ttl);
                case 'file':
                    return Cache::store('file')->set($key, $value, $ttl);
                default:
                    return Cache::set($key, $value, $ttl);
            }
        } catch (\Exception $e) {
            Log::warning("设置到缓存级别 {$level} 失败", [
                'key' => $key,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    // 内存缓存操作方法
    private function getFromMemory(string $key)
    {
        if (!isset(self::$memoryCache[$key])) {
            return null;
        }

        $item = self::$memoryCache[$key];

        // 检查是否过期
        if ($item['expires'] < time()) {
            unset(self::$memoryCache[$key]);
            return null;
        }

        return $item['value'];
    }

    private function setToMemory(string $key, $value, int $ttl): bool
    {
        // 限制内存缓存大小
        if (count(self::$memoryCache) > 1000) {
            $this->cleanupMemoryCache();
        }

        self::$memoryCache[$key] = [
            'value' => $value,
            'expires' => time() + $ttl,
            'created' => time()
        ];

        return true;
    }

    /**
     * 清理内存缓存
     */
    private function cleanupMemoryCache(): void
    {
        $now = time();
        $cleaned = 0;

        foreach (self::$memoryCache as $key => $item) {
            if ($item['expires'] < $now) {
                unset(self::$memoryCache[$key]);
                $cleaned++;
            }
        }

        // 如果清理后还是太多，删除最老的一半
        if (count(self::$memoryCache) > 800) {
            uasort(self::$memoryCache, function($a, $b) {
                return $a['created'] - $b['created'];
            });

            $toRemove = array_slice(array_keys(self::$memoryCache), 0, 400);
            foreach ($toRemove as $key) {
                unset(self::$memoryCache[$key]);
            }
        }

        Log::debug("内存缓存清理完成", [
            'cleaned_expired' => $cleaned,
            'remaining_items' => count(self::$memoryCache)
        ]);
    }

    /**
     * 获取默认TTL
     */
    private function getDefaultTTL(string $key): int
    {
        foreach (self::CACHE_TIMES as $pattern => $ttl) {
            if (strpos($key, $pattern) !== false) {
                return $ttl;
            }
        }
        return 3600; // 默认1小时
    }

    /**
     * 数据压缩 (如果需要)
     */
    private function compressIfNeeded($value): string
    {
        $serialized = serialize($value);

        // 大于1KB的数据进行压缩
        if (strlen($serialized) > 1024) {
            $this->stats['compressions']++;
            return 'compressed:' . gzcompress($serialized, 6);
        }

        return $serialized;
    }

    /**
     * 数据解压 (如果需要)
     */
    private function decompressIfNeeded($value)
    {
        if (is_string($value) && strpos($value, 'compressed:') === 0) {
            $compressed = substr($value, 11); // 移除 'compressed:' 前缀
            $decompressed = gzuncompress($compressed);
            return unserialize($decompressed);
        }

        return unserialize($value);
    }

    /**
     * 设置缓存标签
     */
    private function setCacheTags(string $key): void
    {
        foreach (self::CACHE_TAGS as $tag => $patterns) {
            foreach ($patterns as $pattern) {
                if (strpos($key, $pattern) !== false) {
                    try {
                        Cache::tag($tag)->set($key . '_tag', true, 86400);
                    } catch (\Exception $e) {
                        Log::debug("设置缓存标签失败", [
                            'tag' => $tag,
                            'key' => $key,
                            'error' => $e->getMessage()
                        ]);
                    }
                    break;
                }
            }
        }
    }

    /**
     * 构建分类树
     */
    private function buildCategoryTree(): array
    {
        $categories = Db::table('video_categories')
            ->where('status', 1)
            ->order('sort_order', 'asc')
            ->select()
            ->toArray();

        return $this->buildTree($categories);
    }

    /**
     * 递归构建树结构
     */
    private function buildTree(array $categories, int $parentId = 0): array
    {
        $tree = [];

        foreach ($categories as $category) {
            if ($category['parent_id'] == $parentId) {
                $category['children'] = $this->buildTree($categories, $category['id']);
                $tree[] = $category;
            }
        }

        return $tree;
    }

    /**
     * 记录缓存操作日志
     */
    private function logCacheOperation(string $operation, string $key, string $level): void
    {
        if (Config::get('app.debug', false)) {
            Log::debug('缓存操作', [
                'operation' => $operation,
                'key' => substr($key, 0, 50) . (strlen($key) > 50 ? '...' : ''),
                'level' => $level,
                'timestamp' => microtime(true)
            ]);
        }
    }

    /**
     * 按标签清除缓存
     */
    public function clearByTag(string $tag): bool
    {
        try {
            if (!isset(self::CACHE_TAGS[$tag])) {
                return false;
            }

            $patterns = self::CACHE_TAGS[$tag];
            $success = true;

            foreach ($patterns as $pattern) {
                // 清除匹配的缓存键
                $this->clearCacheByPattern($pattern);
            }

            // 清除标签缓存
            try {
                Cache::tag($tag)->clear();
            } catch (\Exception $e) {
                Log::warning("清除标签缓存失败", [
                    'tag' => $tag,
                    'error' => $e->getMessage()
                ]);
                $success = false;
            }

            Log::info('按标签清除缓存', ['tag' => $tag, 'success' => $success]);
            return $success;

        } catch (\Exception $e) {
            Log::error('按标签清除缓存失败', [
                'tag' => $tag,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 防缓存雪崩 - 随机TTL
     */
    private function getRandomizedTTL(int $baseTTL): int
    {
        // 在基础TTL的基础上增加10%-30%的随机时间
        $randomFactor = mt_rand(10, 30) / 100;
        return $baseTTL + (int)($baseTTL * $randomFactor);
    }

    /**
     * 缓存健康检查
     */
    public function healthCheck(): array
    {
        $health = [
            'status' => 'healthy',
            'checks' => []
        ];

        // 检查Redis连接
        try {
            Cache::store('redis')->set('health_check', time(), 10);
            $value = Cache::store('redis')->get('health_check');
            $health['checks']['redis'] = $value ? 'ok' : 'failed';
        } catch (\Exception $e) {
            $health['checks']['redis'] = 'failed';
            $health['status'] = 'unhealthy';
        }

        // 检查文件缓存
        try {
            Cache::store('file')->set('health_check', time(), 10);
            $value = Cache::store('file')->get('health_check');
            $health['checks']['file'] = $value ? 'ok' : 'failed';
        } catch (\Exception $e) {
            $health['checks']['file'] = 'failed';
            $health['status'] = 'unhealthy';
        }

        // 检查内存使用
        $memoryUsage = memory_get_usage(true);
        $memoryLimit = $this->parseMemoryLimit(ini_get('memory_limit'));
        $memoryPercent = $memoryLimit > 0 ? ($memoryUsage / $memoryLimit) * 100 : 0;

        $health['checks']['memory'] = $memoryPercent < 80 ? 'ok' : 'warning';
        $health['memory_usage_percent'] = round($memoryPercent, 2);

        return $health;
    }

    /**
     * 解析内存限制
     */
    private function parseMemoryLimit(string $limit): int
    {
        $limit = trim($limit);
        $last = strtolower($limit[strlen($limit)-1]);
        $value = (int)$limit;

        switch($last) {
            case 'g': $value *= 1024;
            case 'm': $value *= 1024;
            case 'k': $value *= 1024;
        }

        return $value;
    }
}
