<?php
// +----------------------------------------------------------------------
// | 管理员API路由配置
// +----------------------------------------------------------------------
// | 统一的管理员后台API路由管理
// +----------------------------------------------------------------------

use think\facade\Route;

// =====================================================
// 管理员API路由组
// =====================================================

Route::group('api/admin', function () {
    
    // =====================================================
    // 管理员认证接口（无需认证）
    // =====================================================
    
    Route::post('login', 'admin/Auth/login');
    
    // =====================================================
    // 需要管理员认证的接口
    // =====================================================
    
    Route::group('', function () {
        
        // 认证相关
        Route::get('auth/verify', 'admin/Auth/verify');
        Route::get('profile', 'admin/Auth/profile');
        Route::post('logout', 'admin/Auth/logout');
        
        // =====================================================
        // 仪表盘相关
        // =====================================================
        
        Route::group('dashboard', function () {
            Route::get('', 'admin/Dashboard/index');
            Route::get('stats', 'admin/Dashboard/stats');
        });
        
        // =====================================================
        // 用户管理
        // =====================================================
        
        Route::resource('users', 'admin/User');
        
        // =====================================================
        // 视频管理
        // =====================================================
        
        Route::group('videos', function () {
            Route::get('', 'admin/Video/index');
            Route::get(':id', 'admin/Video/read');
            Route::post('', 'admin/Video/save');
            Route::put(':id', 'admin/Video/update');
            Route::delete(':id', 'admin/Video/delete');
            Route::get('stats', 'admin/Video/stats');
            Route::put(':id/audit', 'admin/Video/audit');
        });
        
        // =====================================================
        // 分类管理
        // =====================================================
        
        Route::group('categories', function () {
            Route::get('', 'admin/Category/index');
            Route::get(':id', 'admin/Category/read');
            Route::post('', 'admin/Category/save');
            Route::put(':id', 'admin/Category/update');
            Route::delete(':id', 'admin/Category/delete');
            Route::get('parents/list', 'admin/Category/getParentCategories');
        });
        
        // =====================================================
        // 系统配置管理
        // =====================================================

        Route::group('config', function () {
            Route::get('', 'admin/Config/index');
            Route::get(':key', 'admin/Config/read');
            Route::post('', 'admin/Config/save');
            Route::put(':key', 'admin/Config/update');
            Route::delete(':key', 'admin/Config/delete');
        });

        // 系统设置路由（兼容前端API路径）
        Route::group('settings', function () {
            Route::get('', 'admin/Config/index');
            Route::put('', 'admin/Config/batchUpdate');
            Route::get('website', 'admin/Config/website');
            Route::put('website', 'admin/Config/updateWebsite');
            Route::get('player', 'admin/Config/player');
            Route::put('player', 'admin/Config/updatePlayer');
            Route::post('test-database', 'admin/Settings/testDatabase');
            Route::post('test-video-processing', 'admin/Settings/testVideoProcessing');
            Route::post('test-cloud-storage', 'admin/Settings/testCloudStorage');
        });
        
        // =====================================================
        // 文件上传管理
        // =====================================================
        
        Route::group('upload', function () {
            Route::post('image', 'admin/Upload/image');
            Route::post('video', 'admin/Upload/video');
            Route::post('file', 'admin/Upload/file');
            Route::get('records', 'admin/Upload/getRecords');
            Route::delete('records/:id', 'admin/Upload/deleteRecord');
        });
        
        // =====================================================
        // 采集管理 - 保留所有现有功能
        // =====================================================

        Route::group('collect', function () {
            // 采集源管理 - 保持原有路由顺序
            Route::post('sources/test', 'admin/Collect/testSource'); // 测试采集源 - 必须在sources之前
            Route::get('sources', 'admin/Collect/getSources'); // 获取采集源列表
            Route::post('sources', 'admin/Collect/addSource'); // 添加采集源
            Route::put('sources/:id', 'admin/Collect/updateSource'); // 更新采集源
            Route::delete('sources/:id', 'admin/Collect/deleteSource'); // 删除采集源

            // 分类映射管理
            Route::get('categories/:source_id', 'admin/Collect/getRemoteCategories'); // 获取远程分类
            Route::post('categories/mapping', 'admin/Collect/saveCategoryMapping'); // 保存分类映射

            // 采集配置管理
            Route::get('config', 'admin/Collect/getConfig'); // 获取采集配置
            Route::put('config', 'admin/Collect/updateConfig'); // 更新采集配置

            // 采集任务管理 - 更具体的路由放在前面
            Route::post('tasks/:id/start', 'admin/Collect/startTaskById'); // 启动指定采集任务
            Route::post('tasks/:id/execute', 'admin/Collect/executeTaskSync'); // 同步执行采集任务（测试用）
            Route::post('tasks/:id/stop', 'admin/Collect/stopTaskById'); // 停止指定采集任务
            Route::delete('tasks/:id', 'admin/Collect/deleteTask'); // 删除采集任务
            Route::post('tasks/start', 'admin/Collect/startTask'); // 启动采集任务
            Route::get('tasks', 'admin/Collect/getTasks'); // 获取采集任务列表
            Route::post('tasks', 'admin/Collect/createTask'); // 创建采集任务

            // 采集日志
            Route::get('logs', 'admin/Collect/getLogs'); // 获取采集日志
        });
        
        // =====================================================
        // 视频处理管理
        // =====================================================

        Route::group('video-processing', function () {
            Route::get('status', 'admin/VideoProcessing/status');
            Route::post('start-worker', 'admin/VideoProcessing/startWorker');
            Route::post('stop-worker', 'admin/VideoProcessing/stopWorker');
            Route::post('restart-worker', 'admin/VideoProcessing/restartWorker');
            Route::post('batch-reprocess', 'admin/VideoProcessing/batchReprocess');
            Route::post('clear-queue', 'admin/VideoProcessing/clearQueue');
        });

        // =====================================================
        // 系统监控
        // =====================================================

        Route::group('monitor', function () {
            Route::get('system', 'admin/Monitor/system');
            Route::get('database', 'admin/Monitor/database');
            Route::get('logs', 'admin/Monitor/logs');
            Route::get('performance', 'admin/Monitor/performance');
        });
        
        // =====================================================
        // 操作日志
        // =====================================================
        
        Route::group('logs', function () {
            Route::get('operations', 'admin/Log/operations');
            Route::get('errors', 'admin/Log/errors');
            Route::get('access', 'admin/Log/access');
        });
        
    })->middleware(\app\middleware\AdminAuth::class);
    
})->middleware([\app\middleware\Cors::class, \app\middleware\ApiKeyAuth::class]);
